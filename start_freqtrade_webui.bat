@echo off
echo Starting FreqTrade with Web UI...

REM Stop any running containers first
docker compose down

REM Start FreqTrade with the web UI enabled
docker compose run --rm -p 8081:8080 freqtrade webserver ^
  --config user_data/config_backtest_machetev8b.json ^
  --no-telegram

echo.
echo FreqTrade Web UI started! Access it at http://localhost:8081
echo.
echo You can now run backtests for any strategy through the web interface.
echo Press Ctrl+C to stop the server when done.
