from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame
import pandas as pd
import numpy as np
from freqtrade.persistence import Trade
from datetime import datetime
import freqtrade.vendor.qtpylib.indicators as qtpylib
import talib.abstract as ta

class Pivot7Enhanced(IStrategy):
    stoploss = -0.5
    can_short = True
    trailing_stop = False
    trailing_stop_positive = 0.1
    trailing_stop_positive_offset = 0.5


    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        length = 25
        dataframe['rsi_3'] = ta.RSI(dataframe, timeperiod=2)
        dataframe['rsi_6'] = ta.RSI(dataframe, timeperiod=4)

        dataframe['pivot_high'] = dataframe['high'][::-1].rolling(window=length, min_periods=1).max()[::-1]
        dataframe['pivot_high'] = dataframe['pivot_high'].where(dataframe['high'] == dataframe['pivot_high'], np.nan)

        dataframe['pivot_low'] = dataframe['low'][::-1].rolling(window=length, min_periods=1).min()[::-1]
        dataframe['pivot_low'] = dataframe['pivot_low'].where(dataframe['low'] == dataframe['pivot_low'], np.nan)

        dataframe['high_swing'] = dataframe['pivot_high'].shift(length // 2)
        dataframe['low_swing'] = dataframe['pivot_low'].shift(length // 2)

        dataframe['high_swing'].fillna(0, inplace=True)
        dataframe['low_swing'].fillna(0, inplace=True)

        # Enhanced indicators for signal strength
        dataframe['volume_sma'] = dataframe['volume'].rolling(window=20).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']

        # RSI divergence strength
        dataframe['rsi_divergence'] = abs(dataframe['rsi_3'] - dataframe['rsi_6'])

        # Price distance from pivot (normalized)
        dataframe['pivot_distance_long'] = np.where(
            dataframe['low_swing'] != 0,
            abs(dataframe['close'] - dataframe['low_swing']) / dataframe['close'],
            0
        )
        dataframe['pivot_distance_short'] = np.where(
            dataframe['high_swing'] != 0,
            abs(dataframe['close'] - dataframe['high_swing']) / dataframe['close'],
            0
        )

        # Signal strength calculation
        dataframe['signal_strength'] = 1.0

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:

        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0

        # Long entries
        long_condition = (
            (qtpylib.crossed_above(dataframe['rsi_3'], dataframe['rsi_6'])) &
            (dataframe['low_swing'] != 0) &
            (dataframe['close'] > dataframe['low_swing'].shift(1)) &
            (dataframe['enter_long'].shift().fillna(0) == 0) &
            (dataframe['enter_short'].shift().fillna(0) == 0)
        )

        dataframe.loc[long_condition, 'enter_long'] = 1

        # Calculate signal strength for long entries
        for i in range(len(dataframe)):
            if dataframe['enter_long'].iloc[i] == 1:
                strength = self.calculate_signal_strength(dataframe, i, 'long')
                dataframe.loc[dataframe.index[i], 'signal_strength'] = strength

        # Short entries
        short_condition = (
            (qtpylib.crossed_below(dataframe['rsi_3'], dataframe['rsi_6'])) &
            (dataframe['high_swing'] != 0) &
            (dataframe['close'] < dataframe['high_swing'].shift(1)) &
            (dataframe['enter_long'].shift().fillna(0) == 0) &
            (dataframe['enter_short'].shift().fillna(0) == 0)
        )

        dataframe.loc[short_condition, 'enter_short'] = 1

        # Calculate signal strength for short entries
        for i in range(len(dataframe)):
            if dataframe['enter_short'].iloc[i] == 1:
                strength = self.calculate_signal_strength(dataframe, i, 'short')
                dataframe.loc[dataframe.index[i], 'signal_strength'] = strength

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:

        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0

        dataframe.loc[
            (qtpylib.crossed_above(dataframe['rsi_3'], dataframe['rsi_6'])) &
            (dataframe['low_swing'] != 0) &
            (dataframe['close'] > dataframe['low_swing'].shift(1)) &
            (dataframe['enter_long'].shift().fillna(0) == 0) &  # Перевірка на відсутність попередніх сигналів
            (dataframe['enter_short'].shift().fillna(0) == 0),  # Перевірка на відсутність попередніх сигналів
            'exit_short'
        ] = 1

        dataframe.loc[
            (qtpylib.crossed_below(dataframe['rsi_3'], dataframe['rsi_6'])) &
            (dataframe['high_swing'] != 0) &
            (dataframe['close'] < dataframe['high_swing'].shift(1)) &
            (dataframe['enter_long'].shift().fillna(0) == 0) &  # Перевірка на відсутність попередніх сигналів
            (dataframe['enter_short'].shift().fillna(0) == 0),  # Перевірка на відсутність попередніх сигналів
            'exit_long'
        ] = 1

        return dataframe

    def calculate_signal_strength(self, dataframe: DataFrame, index: int, side: str) -> float:
        """Calculate signal strength based on multiple factors"""
        if index < 20:  # Need enough data
            return 1.0

        # Base strength
        strength = 1.0

        # Factor 1: RSI divergence strength (0.8 to 1.3 multiplier)
        rsi_div = dataframe['rsi_divergence'].iloc[index]
        rsi_factor = min(max(0.8, rsi_div / 20), 1.3)

        # Factor 2: Volume confirmation (0.7 to 1.4 multiplier)
        vol_ratio = dataframe['volume_ratio'].iloc[index]
        vol_factor = min(max(0.7, vol_ratio / 2), 1.4)

        # Factor 3: Pivot distance (0.6 to 1.5 multiplier)
        if side == 'long':
            pivot_dist = dataframe['pivot_distance_long'].iloc[index]
        else:
            pivot_dist = dataframe['pivot_distance_short'].iloc[index]

        pivot_factor = min(max(0.6, pivot_dist * 50), 1.5)

        # Combine factors
        strength = rsi_factor * vol_factor * pivot_factor

        # Normalize to 0.5 - 2.0 range
        return min(max(strength, 0.5), 2.0)

    def custom_stake_amount(self, pair: str, current_time: datetime,
                           current_rate: float, proposed_stake: float,
                           min_stake: float, max_stake: float, **kwargs) -> float:
        """Dynamic stake amount based on signal strength"""

        try:
            # Get the latest signal strength from dataframe
            dataframe = self.dp.get_pair_dataframe(pair, self.timeframe)
            if len(dataframe) == 0 or 'signal_strength' not in dataframe.columns:
                return 100  # Default stake

            # Get the most recent signal strength
            latest_strength = dataframe['signal_strength'].iloc[-1]
            if latest_strength == 0 or pd.isna(latest_strength):
                latest_strength = 1.0  # Default if no signal

            # Base stake amount
            base_stake = 100

            # Dynamic stake: 50 to 200 USDT based on signal strength (0.5 to 2.0)
            dynamic_stake = base_stake * latest_strength

            # Ensure within bounds
            return min(max(dynamic_stake, min_stake), min(max_stake, 200))
        except:
            return 100  # Fallback to default

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:
        """Dynamic leverage based on signal strength"""

        try:
            # Get the latest signal strength from dataframe
            dataframe = self.dp.get_pair_dataframe(pair, self.timeframe)
            if len(dataframe) == 0 or 'signal_strength' not in dataframe.columns:
                return 15  # Conservative default

            # Get the most recent signal strength
            latest_strength = dataframe['signal_strength'].iloc[-1]
            if latest_strength == 0 or pd.isna(latest_strength):
                latest_strength = 1.0  # Default if no signal

            # Dynamic leverage: 10x to 25x based on signal strength
            base_leverage = 10
            max_additional = 15

            # Calculate dynamic leverage
            dynamic_leverage = base_leverage + (max_additional * (latest_strength - 0.5) / 1.5)

            # Ensure within exchange limits
            return min(max(dynamic_leverage, 5), min(max_leverage, 25))
        except:
            return 15  # Fallback to conservative default
