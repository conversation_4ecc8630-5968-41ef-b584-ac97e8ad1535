from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame
import numpy as np
from freqtrade.persistence import Trade
from datetime import datetime
import freqtrade.vendor.qtpylib.indicators as qtpylib
import talib.abstract as ta

class pivot7_live(IStrategy):
    stoploss = -0.5
    can_short = True
    trailing_stop = True
    trailing_stop_positive = 0.1
    trailing_stop_positive_offset = 0.5
    
    timeframe = '15m'

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        length = 25
        dataframe['rsi_3'] = ta.RSI(dataframe['close'], timeperiod=2)
        dataframe['rsi_6'] = ta.RSI(dataframe['close'], timeperiod=4)

        # Remove lookahead bias - calculate pivots without future data
        dataframe['pivot_high'] = dataframe['high'].rolling(window=length, min_periods=1).max().shift(length//2)
        dataframe['pivot_high'] = dataframe['pivot_high'].where(
            dataframe['high'].shift(length//2) == dataframe['pivot_high'], np.nan
        )

        dataframe['pivot_low'] = dataframe['low'].rolling(window=length, min_periods=1).min().shift(length//2)
        dataframe['pivot_low'] = dataframe['pivot_low'].where(
            dataframe['low'].shift(length//2) == dataframe['pivot_low'], np.nan
        )

        dataframe['high_swing'] = dataframe['pivot_high']
        dataframe['low_swing'] = dataframe['pivot_low']

        dataframe['high_swing'] = dataframe['high_swing'].fillna(0)
        dataframe['low_swing'] = dataframe['low_swing'].fillna(0)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0

        dataframe.loc[
            (qtpylib.crossed_above(dataframe['rsi_3'], dataframe['rsi_6'])) &
            (dataframe['low_swing'] != 0) & 
            (dataframe['close'] > dataframe['low_swing']),
            'enter_long'
        ] = 1

        dataframe.loc[
            (qtpylib.crossed_below(dataframe['rsi_3'], dataframe['rsi_6'])) &
            (dataframe['high_swing'] != 0) & 
            (dataframe['close'] < dataframe['high_swing']),
            'enter_short'
        ] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0

        dataframe.loc[
            (qtpylib.crossed_above(dataframe['rsi_3'], dataframe['rsi_6'])) &
            (dataframe['low_swing'] != 0) & 
            (dataframe['close'] > dataframe['low_swing']),
            'exit_short'
        ] = 1

        dataframe.loc[
            (qtpylib.crossed_below(dataframe['rsi_3'], dataframe['rsi_6'])) &
            (dataframe['high_swing'] != 0) & 
            (dataframe['close'] < dataframe['high_swing']),
            'exit_long'
        ] = 1

        return dataframe
        
    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:
        return 20