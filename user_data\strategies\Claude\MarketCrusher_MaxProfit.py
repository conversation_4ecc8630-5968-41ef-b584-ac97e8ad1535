from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame
import numpy as np
from freqtrade.persistence import Trade
from datetime import datetime, timedelta
import freqtrade.vendor.qtpylib.indicators as qtpylib
import talib.abstract as ta
from typing import Optional
import logging

class MarketCrusher_MaxProfit(IStrategy):
    """
    MAXIMUM PROFITABILITY STRATEGY
    Designed to extract every possible edge from the market.
    This is what you use when you want to get RICH.
    """
    
    # AGGRESSIVE SETTINGS - We're here to make money, not play it safe
    stoploss = -0.25  # Wide stop to ride through noise
    can_short = True
    timeframe = '15m'
    
    # DYNAMIC TRAILING - Ride winners hard
    trailing_stop = True
    trailing_stop_positive = 0.001  # Trail from 0.1% profit
    trailing_stop_positive_offset = 0.008  # 0.8% trail distance
    
    # PROFIT TAKING - But let winners run
    minimal_roi = {
        "0": 0.50,     # 50% max profit (let it run!)
        "30": 0.15,    # 15% after 30min
        "60": 0.08,    # 8% after 1h
        "120": 0.04,   # 4% after 2h
        "240": 0.02,   # 2% after 4h
        "480": 0.01    # 1% after 8h
    }
    
    startup_candle_count = 500
    
    # MARKET DOMINATION PARAMETERS
    min_profit_multiplier = 3.0  # Risk/Reward ratio
    max_drawdown_protection = 0.15  # 15% max drawdown before going defensive
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """THE ULTIMATE MARKET ANALYSIS ENGINE"""
        
        # ========== HYPER-OPTIMIZED RSI SYSTEM ==========
        # Multi-fractal RSI analysis
        rsi_periods = [2, 3, 5, 8, 13, 14, 21, 34]  # Added 14 for standard RSI
        for period in rsi_periods:
            dataframe[f'rsi_{period}'] = ta.RSI(dataframe['close'], timeperiod=period)
        
        # RSI derivatives for acceleration detection
        dataframe['rsi_velocity'] = dataframe['rsi_8'].diff()
        dataframe['rsi_acceleration'] = dataframe['rsi_velocity'].diff()
        
        # Composite RSI strength
        dataframe['rsi_strength'] = (
            (dataframe['rsi_2'] > 50).astype(int) * 4 +
            (dataframe['rsi_3'] > 50).astype(int) * 3 +
            (dataframe['rsi_5'] > 50).astype(int) * 2 +
            (dataframe['rsi_8'] > 50).astype(int) * 2 +
            (dataframe['rsi_13'] > 50).astype(int) * 1
        ) / 12 * 100  # Normalized to 0-100
        
        # ========== ADVANCED PIVOT SYSTEM ==========
        # Multiple pivot detection algorithms
        pivot_lengths = [5, 8, 13, 21, 34, 55]
        
        for length in pivot_lengths:
            # Traditional pivots
            dataframe[f'pivot_high_{length}'] = dataframe['high'].rolling(
                window=length*2+1, center=True
            ).max().shift(1)  # Slight shift to avoid lookahead
            
            dataframe[f'pivot_low_{length}'] = dataframe['low'].rolling(
                window=length*2+1, center=True
            ).min().shift(1)
            
            # Only mark true extremes
            dataframe[f'pivot_high_{length}'] = dataframe[f'pivot_high_{length}'].where(
                dataframe['high'].shift(1) == dataframe[f'pivot_high_{length}'], np.nan
            )
            dataframe[f'pivot_low_{length}'] = dataframe[f'pivot_low_{length}'].where(
                dataframe['low'].shift(1) == dataframe[f'pivot_low_{length}'], np.nan
            )
        
        # SMART SUPPORT/RESISTANCE - Multiple algorithms combined
        recent_highs = [dataframe[f'pivot_high_{l}'].ffill() for l in [13, 21, 34]]
        recent_lows = [dataframe[f'pivot_low_{l}'].ffill() for l in [13, 21, 34]]
        
        dataframe['smart_resistance'] = np.maximum.reduce(recent_highs)
        dataframe['smart_support'] = np.minimum.reduce(recent_lows)
        
        # ========== TREND SYSTEMS ==========
        # Exponential Moving Averages - Full spectrum
        ema_periods = [3, 5, 8, 13, 21, 34, 55, 89, 144, 233]
        for period in ema_periods:
            dataframe[f'ema_{period}'] = ta.EMA(dataframe, timeperiod=period)
        
        # TREND STRENGTH CALCULATION
        dataframe['trend_power'] = (
            (dataframe['ema_8'] - dataframe['ema_21']) / dataframe['close'] * 100
        )
        
        # Multi-timeframe trend alignment
        short_trend = (dataframe['ema_8'] > dataframe['ema_13']).astype(int)
        medium_trend = (dataframe['ema_21'] > dataframe['ema_34']).astype(int)
        long_trend = (dataframe['ema_55'] > dataframe['ema_89']).astype(int)
        
        dataframe['trend_alignment'] = short_trend + medium_trend + long_trend  # 0-3 scale
        
        # ========== VOLATILITY & MOMENTUM ==========
        # Advanced ATR calculations
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)
        dataframe['atr_percent'] = dataframe['atr'] / dataframe['close'] * 100
        
        # Volatility regimes
        dataframe['vol_regime'] = np.where(
            dataframe['atr_percent'] > dataframe['atr_percent'].rolling(100).quantile(0.8), 'HIGH',
            np.where(dataframe['atr_percent'] < dataframe['atr_percent'].rolling(100).quantile(0.2), 'LOW', 'MEDIUM')
        )
        
        # MOMENTUM INDICATORS - The full arsenal
        dataframe['macd'], dataframe['macdsignal'], dataframe['macdhist'] = ta.MACD(dataframe['close'], fastperiod=8, slowperiod=21, signalperiod=5)
        dataframe['macd_fast'], _, _ = ta.MACD(dataframe['close'], fastperiod=5, slowperiod=13, signalperiod=3)

        # Stochastic family
        dataframe['stoch_k'], dataframe['stoch_d'] = ta.STOCH(dataframe['high'], dataframe['low'], dataframe['close'], fastk_period=5, slowk_period=3, slowd_period=3)
        dataframe['stoch_rsi_k'], dataframe['stoch_rsi_d'] = ta.STOCHRSI(dataframe['close'], timeperiod=14)

        # Other oscillators
        dataframe['cci'] = ta.CCI(dataframe['high'], dataframe['low'], dataframe['close'], timeperiod=14)
        dataframe['williams_r'] = ta.WILLR(dataframe['high'], dataframe['low'], dataframe['close'], timeperiod=14)
        dataframe['ultimate_osc'] = ta.ULTOSC(dataframe['high'], dataframe['low'], dataframe['close'])
        
        # ========== VOLUME ANALYSIS ==========
        # Volume indicators
        dataframe['volume_sma'] = dataframe['volume'].rolling(20).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        dataframe['volume_surge'] = dataframe['volume'] > dataframe['volume_sma'] * 2
        
        # Advanced volume analysis
        dataframe['vwap'] = qtpylib.rolling_vwap(dataframe, window=20)
        dataframe['obv'] = ta.OBV(dataframe['close'], dataframe['volume'])
        dataframe['ad'] = ta.AD(dataframe['high'], dataframe['low'], dataframe['close'], dataframe['volume'])  # Accumulation/Distribution
        dataframe['cmf'] = ta.ADOSC(dataframe['high'], dataframe['low'], dataframe['close'], dataframe['volume'], fastperiod=3, slowperiod=10)  # Chaikin Money Flow

        # Volume-price analysis
        dataframe['price_volume_trend'] = ta.OBV(dataframe['close'], dataframe['volume']) / dataframe['volume'].rolling(20).sum()
        
        # ========== MARKET MICROSTRUCTURE ==========
        # Bollinger Bands with multiple deviations
        for std in [1.5, 2.0, 2.5]:
            upper, middle, lower = ta.BBANDS(dataframe['close'], timeperiod=20, nbdevup=std, nbdevdn=std)
            dataframe[f'bb_upper_{std}'] = upper
            dataframe[f'bb_lower_{std}'] = lower
        
        dataframe['bb_position'] = (dataframe['close'] - dataframe['bb_lower_2.0']) / (dataframe['bb_upper_2.0'] - dataframe['bb_lower_2.0'])
        dataframe['bb_squeeze'] = (dataframe['bb_upper_2.0'] - dataframe['bb_lower_2.0']) < (dataframe['bb_upper_2.0'] - dataframe['bb_lower_2.0']).rolling(20).mean() * 0.8
        
        # Keltner Channels for breakout detection
        dataframe['kc_upper'], dataframe['kc_middle'], dataframe['kc_lower'] = ta.BBANDS(dataframe['close'], timeperiod=20, nbdevup=1.5, nbdevdn=1.5)
        dataframe['squeeze'] = (dataframe['bb_upper_2.0'] < dataframe['kc_upper']) & (dataframe['bb_lower_2.0'] > dataframe['kc_lower'])
        
        # ========== PRICE ACTION PATTERNS ==========
        # Candlestick patterns
        dataframe['doji'] = ta.CDLDOJI(dataframe['open'], dataframe['high'], dataframe['low'], dataframe['close'])
        dataframe['hammer'] = ta.CDLHAMMER(dataframe['open'], dataframe['high'], dataframe['low'], dataframe['close'])
        dataframe['shooting_star'] = ta.CDLSHOOTINGSTAR(dataframe['open'], dataframe['high'], dataframe['low'], dataframe['close'])
        dataframe['engulfing_bull'] = ta.CDLENGULFING(dataframe['open'], dataframe['high'], dataframe['low'], dataframe['close'])
        dataframe['morning_star'] = ta.CDLMORNINGSTAR(dataframe['open'], dataframe['high'], dataframe['low'], dataframe['close'])
        dataframe['evening_star'] = ta.CDLEVENINGSTAR(dataframe['open'], dataframe['high'], dataframe['low'], dataframe['close'])
        
        # Price structure
        dataframe['higher_high'] = (dataframe['high'] > dataframe['high'].shift(1)) & (dataframe['high'].shift(1) > dataframe['high'].shift(2))
        dataframe['lower_low'] = (dataframe['low'] < dataframe['low'].shift(1)) & (dataframe['low'].shift(1) < dataframe['low'].shift(2))
        dataframe['inside_bar'] = (dataframe['high'] < dataframe['high'].shift(1)) & (dataframe['low'] > dataframe['low'].shift(1))
        dataframe['outside_bar'] = (dataframe['high'] > dataframe['high'].shift(1)) & (dataframe['low'] < dataframe['low'].shift(1))
        
        # ========== COMPOSITE SIGNALS ==========
        # PROFIT PROBABILITY SCORE (0-100)
        momentum_signals = [
            (dataframe['rsi_strength'] > 60).astype(int) * 15,
            (dataframe['macd'] > dataframe['macdsignal']).astype(int) * 10,
            (dataframe['stoch_k'] > dataframe['stoch_d']).astype(int) * 8,
            (dataframe['cci'] > 0).astype(int) * 7,
            (dataframe['ultimate_osc'] > 50).astype(int) * 6,
            (dataframe['trend_alignment'] >= 2).astype(int) * 12,
            (dataframe['volume_ratio'] > 1.3).astype(int) * 10,
            (dataframe['close'] > dataframe['vwap']).astype(int) * 8,
            (dataframe['obv'] > dataframe['obv'].shift(5)).astype(int) * 6,
            (abs(dataframe['trend_power']) > 1.0).astype(int) * 8,
            (dataframe['bb_position'] > 0.3).astype(int) * 5,
            (dataframe['bb_position'] < 0.7).astype(int) * 5
        ]
        
        dataframe['profit_probability'] = sum(momentum_signals)
        
        # RISK ASSESSMENT
        risk_factors = [
            (dataframe['vol_regime'] == 'HIGH').astype(int) * -15,
            (dataframe['bb_position'] > 0.9).astype(int) * -10,
            (dataframe['bb_position'] < 0.1).astype(int) * -10,
            (dataframe['rsi_14'] > 80).astype(int) * -8,
            (dataframe['rsi_14'] < 20).astype(int) * -8,
            (dataframe['volume_ratio'] < 0.5).astype(int) * -12,
            (dataframe['squeeze']).astype(int) * -5
        ]
        
        dataframe['risk_score'] = sum(risk_factors)
        
        # FINAL EDGE CALCULATION
        dataframe['edge_score'] = dataframe['profit_probability'] + dataframe['risk_score']
        
        # ========== MARKET REGIME DETECTION ==========
        # Long-term trend
        dataframe['regime'] = np.where(
            dataframe['ema_89'] > dataframe['ema_144'], 'BULL',
            np.where(dataframe['ema_89'] < dataframe['ema_144'], 'BEAR', 'NEUTRAL')
        )
        
        # Market phase detection
        dataframe['phase'] = np.where(
            dataframe['squeeze'], 'COMPRESSION',
            np.where(dataframe['volume_surge'] & (abs(dataframe['trend_power']) > 2), 'BREAKOUT', 'TRENDING')
        )
        
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """MAXIMUM EDGE ENTRY SYSTEM"""
        
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        
        # ========== CORE SIGNAL DETECTION ==========
        # Primary RSI cross signal
        primary_long = qtpylib.crossed_above(dataframe['rsi_2'], dataframe['rsi_3'])
        primary_short = qtpylib.crossed_below(dataframe['rsi_2'], dataframe['rsi_3'])
        
        # Secondary confirmation
        secondary_long = qtpylib.crossed_above(dataframe['rsi_3'], dataframe['rsi_5'])
        secondary_short = qtpylib.crossed_below(dataframe['rsi_3'], dataframe['rsi_5'])
        
        # ========== HIGH PROBABILITY FILTERS ==========
        # Only trade when edge score is exceptional
        high_edge = dataframe['edge_score'] > 75
        
        # Trend alignment requirement
        strong_trend_up = dataframe['trend_alignment'] >= 2
        strong_trend_down = dataframe['trend_alignment'] <= 1
        
        # Momentum confirmation
        momentum_up = (
            (dataframe['macd'] > dataframe['macdsignal']) &
            (dataframe['macdhist'] > 0) &
            (dataframe['rsi_strength'] > 55)
        )
        
        momentum_down = (
            (dataframe['macd'] < dataframe['macdsignal']) &
            (dataframe['macdhist'] < 0) &
            (dataframe['rsi_strength'] < 45)
        )
        
        # Volume explosion
        volume_confirmation = dataframe['volume_ratio'] > 1.5
        
        # Support/Resistance positioning
        sr_long = (
            (dataframe['close'] > dataframe['smart_support']) &
            (dataframe['close'] < dataframe['smart_resistance'] * 0.95)
        )
        
        sr_short = (
            (dataframe['close'] < dataframe['smart_resistance']) &
            (dataframe['close'] > dataframe['smart_support'] * 1.05)
        )
        
        # Volatility environment
        good_volatility = dataframe['vol_regime'].isin(['MEDIUM', 'HIGH'])
        
        # ========== BREAKOUT DETECTION ==========
        # Squeeze breakouts - highest probability trades
        squeeze_breakout_long = (
            dataframe['squeeze'].shift(1) &
            (~dataframe['squeeze']) &
            (dataframe['close'] > dataframe['bb_upper_1.5']) &
            (dataframe['volume_ratio'] > 2.0)
        )
        
        squeeze_breakout_short = (
            dataframe['squeeze'].shift(1) &
            (~dataframe['squeeze']) &
            (dataframe['close'] < dataframe['bb_lower_1.5']) &
            (dataframe['volume_ratio'] > 2.0)
        )
        
        # ========== MARKET REGIME ADAPTATIONS ==========
        # Bull market conditions
        bull_market_long = (
            (dataframe['regime'] == 'BULL') &
            (dataframe['rsi_14'] > 35) &
            (dataframe['close'] > dataframe['ema_21'])
        )
        
        # Bear market conditions
        bear_market_short = (
            (dataframe['regime'] == 'BEAR') &
            (dataframe['rsi_14'] < 65) &
            (dataframe['close'] < dataframe['ema_21'])
        )
        
        # Neutral market - only best signals
        neutral_market = (
            (dataframe['regime'] == 'NEUTRAL') &
            (dataframe['edge_score'] > 85)
        )
        
        # ========== FINAL ENTRY CONDITIONS ==========
        # LONG ENTRIES - Multiple paths to victory
        long_conditions = (
            (primary_long | secondary_long) &
            high_edge &
            strong_trend_up &
            momentum_up &
            volume_confirmation &
            sr_long &
            good_volatility &
            (bull_market_long | neutral_market)
        ) | squeeze_breakout_long  # Breakouts override some filters
        
        # SHORT ENTRIES
        short_conditions = (
            (primary_short | secondary_short) &
            high_edge &
            strong_trend_down &
            momentum_down &
            volume_confirmation &
            sr_short &
            good_volatility &
            (bear_market_short | neutral_market)
        ) | squeeze_breakout_short
        
        # POSITION SIZING BASED ON EDGE
        dataframe['position_multiplier'] = np.where(
            dataframe['edge_score'] > 90, 2.0,  # Double size for exceptional signals
            np.where(dataframe['edge_score'] > 85, 1.5, 1.0)
        )
        
        dataframe.loc[long_conditions, 'enter_long'] = dataframe['position_multiplier']
        dataframe.loc[short_conditions, 'enter_short'] = dataframe['position_multiplier']
        
        return dataframe
    
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """PROFIT MAXIMIZATION EXIT SYSTEM"""
        
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        
        # ========== MOMENTUM REVERSAL EXITS ==========
        # Early warning signals
        momentum_reversal_long = (
            (dataframe['rsi_velocity'] < -5) &  # Rapid RSI decline
            (dataframe['macdhist'] < dataframe['macdhist'].shift(1)) &  # MACD weakening
            (dataframe['rsi_14'] > 65)  # From overbought
        )
        
        momentum_reversal_short = (
            (dataframe['rsi_velocity'] > 5) &
            (dataframe['macdhist'] > dataframe['macdhist'].shift(1)) &
            (dataframe['rsi_14'] < 35)
        )
        
        # ========== TREND BREAK EXITS ==========
        trend_break_long = (
            (dataframe['close'] < dataframe['ema_8']) &
            (dataframe['ema_8'] < dataframe['ema_13'])
        )
        
        trend_break_short = (
            (dataframe['close'] > dataframe['ema_8']) &
            (dataframe['ema_8'] > dataframe['ema_13'])
        )
        
        # ========== VOLUME EXHAUSTION ==========
        volume_exhaustion = (
            (dataframe['volume_ratio'] < 0.4) &
            (dataframe['rsi_14'] > 70) | (dataframe['rsi_14'] < 30)
        )
        
        # ========== FINAL EXIT CONDITIONS ==========
        long_exits = (
            momentum_reversal_long |
            trend_break_long |
            (volume_exhaustion & (dataframe['rsi_14'] > 70)) |
            (dataframe['edge_score'] < 30)  # Edge deterioration
        )
        
        short_exits = (
            momentum_reversal_short |
            trend_break_short |
            (volume_exhaustion & (dataframe['rsi_14'] < 30)) |
            (dataframe['edge_score'] < 30)
        )
        
        dataframe.loc[long_exits, 'exit_long'] = 1
        dataframe.loc[short_exits, 'exit_short'] = 1
        
        return dataframe
    
    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """DYNAMIC PROFIT PROTECTION SYSTEM"""
        
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if len(dataframe) == 0:
                return self.stoploss
            
            # Get market conditions
            current_edge = dataframe['edge_score'].iloc[-1]
            current_volatility = dataframe['atr_percent'].iloc[-1]
            trend_power = abs(dataframe['trend_power'].iloc[-1])
            
            # Time in trade
            minutes_in_trade = (current_time - trade.open_date_utc).total_seconds() / 60
            
            # PROFIT-BASED TRAILING
            if current_profit > 0.25:  # 25%+ profit - RIDE IT
                return 0.15  # Only exit on 15% pullback
            elif current_profit > 0.15:  # 15%+ profit
                return max(-0.05, current_profit - 0.08)  # Trail 8% behind
            elif current_profit > 0.08:  # 8%+ profit
                return max(-0.03, current_profit - 0.04)  # Trail 4% behind
            elif current_profit > 0.03:  # 3%+ profit
                return max(-0.015, current_profit - 0.02)  # Trail 2% behind
            elif current_profit > 0.01:  # 1%+ profit
                return -0.005  # Tight breakeven
            
            # EDGE-BASED STOPS
            if current_edge < 20:  # Edge deteriorated badly
                return -0.01
            elif current_edge < 40:  # Moderate edge loss
                return -0.02
            
            # TIME-BASED STOPS
            if minutes_in_trade > 480:  # 8+ hours
                return -0.05
            elif minutes_in_trade > 240:  # 4+ hours
                return -0.08
            
            # VOLATILITY-ADJUSTED INITIAL STOP
            if current_volatility > 3.0:  # High volatility
                return -0.15
            elif current_volatility > 2.0:  # Medium volatility
                return -0.12
            else:  # Low volatility
                return -0.08
                
        except Exception:
            return self.stoploss
    
    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:
        """MAXIMUM PROFIT LEVERAGE SYSTEM"""
        
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if len(dataframe) == 0:
                return 5.0
            
            # Get signal strength
            edge_score = dataframe['edge_score'].iloc[-1]
            trend_power = abs(dataframe['trend_power'].iloc[-1])
            volatility = dataframe['atr_percent'].iloc[-1]
            volume_ratio = dataframe['volume_ratio'].iloc[-1]
            
            # BASE LEVERAGE ON EDGE SCORE
            if edge_score > 95:  # Exceptional signal
                base_leverage = 25.0
            elif edge_score > 90:  # Excellent signal
                base_leverage = 20.0
            elif edge_score > 85:  # Very good signal
                base_leverage = 15.0
            elif edge_score > 80:  # Good signal
                base_leverage = 12.0
            elif edge_score > 75:  # Decent signal
                base_leverage = 8.0
            else:  # Weak signal
                base_leverage = 3.0
            
            # TREND MULTIPLIER
            if trend_power > 3.0:  # Very strong trend
                trend_multiplier = 1.5
            elif trend_power > 2.0:  # Strong trend
                trend_multiplier = 1.3
            elif trend_power > 1.0:  # Medium trend
                trend_multiplier = 1.1
            else:  # Weak trend
                trend_multiplier = 0.8
            
            # VOLATILITY ADJUSTMENT
            if volatility > 4.0:  # Extreme volatility
                vol_multiplier = 0.6
            elif volatility > 2.5:  # High volatility
                vol_multiplier = 0.8
            elif volatility > 1.5:  # Medium volatility
                vol_multiplier = 1.0
            else:  # Low volatility - use more leverage
                vol_multiplier = 1.4
            
            # VOLUME CONFIRMATION
            volume_multiplier = min(1.5, volume_ratio / 2.0) if volume_ratio > 1.0 else 0.7
            
            final_leverage = base_leverage * trend_multiplier * vol_multiplier * volume_multiplier
            
            # Cap at reasonable maximums
            return min(final_leverage, max_leverage, 50.0)
            
        except Exception:
            return 5.0
    
    def confirm_trade_entry(self, pair: str, order_type: str, amount: float,
                           rate: float, time_in_force: str, current_time: datetime,
                           entry_tag: str, side: str, **kwargs) -> bool:
        """FINAL PROFIT VERIFICATION"""
        
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if len(dataframe) == 0:
                return False
            
            # Must have minimum edge score
            edge_score = dataframe['edge_score'].iloc[-1]
            if edge_score < 75:
                return False
            
            # Must have volume
            volume_ratio = dataframe['volume_ratio'].iloc[-1]
            if volume_ratio < 0.8:
                return False
            
            # Avoid extreme market conditions
            volatility = dataframe['atr_percent'].iloc[-1]
            if volatility > 8.0:  # Extremely volatile
                return False
            
            # All systems go - MAKE MONEY!
            return True
            
        except Exception:
            return False
    
    def custom_exit(self, pair: str, trade: Trade, current_time: datetime, current_rate: float,
                   current_profit: float, **kwargs) -> Optional[str]:
        """PROFIT MAXIMIZATION EXIT LOGIC"""
        
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if len(dataframe) == 0:
                return None
            
            # MASSIVE PROFIT PROTECTION
            if current_profit > 0.30:  # 30%+ profit
                edge_score = dataframe['edge_score'].iloc[-1]
                if edge_score < 50:  # Edge is gone, take profits
                    return "profit_protection_30"
            
            # BREAKOUT FAILURE
            if trade.enter_tag and 'breakout' in trade.enter_tag:
                minutes_in_trade = (current_time - trade.open_date_utc).total_seconds() / 60
                if minutes_in_trade > 15 and current_profit < 0.005:  # Breakout failed
                    return "breakout_failure"
            
            return None
            
        except Exception:
            return None