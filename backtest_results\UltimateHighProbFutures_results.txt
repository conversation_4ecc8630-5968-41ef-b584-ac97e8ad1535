2025-06-02 14:57:22,606 - freqtrade - INFO - freqtrade docker-2025.5-dev-f3d03dd2
2025-06-02 14:57:22,859 - numexpr.utils - INFO - NumExpr defaulting to 12 threads.
2025-06-02 14:57:23,935 - freqtrade.configuration.load_config - INFO - Using config: user_data/config_backtest_comparison.json ...
2025-06-02 14:57:23,940 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 14:57:23,940 - root - INFO - Logfile configured
2025-06-02 14:57:23,941 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 14:57:23,942 - freqtrade.configuration.configuration - INFO - Parameter -i/--timeframe detected ... Using timeframe: 5m ...
2025-06-02 14:57:23,942 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 20 ...
2025-06-02 14:57:23,942 - freqtrade.configuration.configuration - INFO - Parameter --dry-run-wallet detected, overriding dry_run_wallet to: 100.0 ...
2025-06-02 14:57:23,943 - freqtrade.configuration.configuration - INFO - Parameter --timerange detected: 20250101- ...
2025-06-02 14:57:26,085 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 14:57:26,087 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-06-02 14:57:26,087 - freqtrade.configuration.configuration - INFO - Overriding timeframe with Command line argument
2025-06-02 14:57:26,088 - freqtrade.configuration.configuration - INFO - Parameter --cache=day detected ...
2025-06-02 14:57:26,088 - freqtrade.configuration.configuration - INFO - Filter trades by timerange: 20250101-
2025-06-02 14:57:26,089 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 14:57:26,100 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-06-02 14:57:26,101 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 14:57:26,101 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 14:57:26,104 - freqtrade.commands.optimize_commands - INFO - Starting freqtrade in Backtesting mode
2025-06-02 14:57:26,105 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-06-02 14:57:26,105 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.82
2025-06-02 14:57:26,105 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'future'}, 'enableRateLimit': True}
2025-06-02 14:57:26,114 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'future'}, 'enableRateLimit': True, 'rateLimit': 200}
2025-06-02 14:57:26,125 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-06-02 14:57:29,191 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2025-06-02 14:57:29,282 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/AdvancedFuturesAIPower.py due to 'No module named 
'freqtrade.freqai.FreqaiStrategy''
2025-06-02 14:57:29,454 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/GodStra.py due to 'No module named 'ta''
2025-06-02 14:57:29,459 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/Heracles.py due to 'No module named 'ta''
2025-06-02 14:57:29,737 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy UltimateHighProbFutures from '/freqtrade/user_data/strategies/ultimate_futures_strategy.py'...
2025-06-02 14:57:29,738 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-02 14:57:29,739 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-02 14:57:29,739 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-06-02 14:57:29,740 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: 5.
2025-06-02 14:57:29,740 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 
'minutes'}.
2025-06-02 14:57:29,740 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 20.
2025-06-02 14:57:29,741 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.08, '10': 0.05, '20': 0.03, '40': 0.02, '80': 0.01}
2025-06-02 14:57:29,742 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-02 14:57:29,742 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.06
2025-06-02 14:57:29,743 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-06-02 14:57:29,743 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.01
2025-06-02 14:57:29,744 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.015
2025-06-02 14:57:29,744 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-02 14:57:29,744 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-02 14:57:29,744 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-02 14:57:29,745 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 
'stoploss_on_exchange_interval': 60}
2025-06-02 14:57:29,745 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-02 14:57:29,745 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-06-02 14:57:29,746 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: 5
2025-06-02 14:57:29,746 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 0
2025-06-02 14:57:29,746 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-02 14:57:29,747 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-02 14:57:29,747 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-02 14:57:29,747 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-06-02 14:57:29,748 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-02 14:57:29,748 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-02 14:57:29,748 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-02 14:57:29,749 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-02 14:57:29,749 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-02 14:57:29,750 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 20
2025-06-02 14:57:29,750 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 14:57:29,768 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-06-02 14:57:29,821 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair AUDIO/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:29,822 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair BTS/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:29,822 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring BLZ/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:29,823 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair EOS/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:29,823 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring FTM/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:29,824 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring KLAY/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:29,824 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair LUNA/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:29,825 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring LINA/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:29,825 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair MATIC/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:29,826 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring OMG/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:29,826 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring OCEAN/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:29,827 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring REEF/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:29,827 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair SRM/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:29,828 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair TOMO/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:29,828 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring UNFI/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:29,828 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring WAVES/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:29,829 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring XEM/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:29,833 - freqtrade.optimize.backtesting - INFO - Using fee 0.0500% - worst case fee from exchange (lowest tier).
2025-06-02 14:57:29,848 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for AAVE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:29,852 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ALICE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:29,854 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ARPA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:29,949 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ANKR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:29,952 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for AXS/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:29,993 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ALGO/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:29,995 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for BAND/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:29,998 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for BEL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,074 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for BAT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,077 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for CHR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,079 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for C98/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,082 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for COTI/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,085 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for CHZ/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,087 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for COMP/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,090 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for CRV/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,092 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for CELO/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,094 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for DUSK/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,133 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for DENT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,135 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for DASH/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,176 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for DYDX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,178 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ENJ/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,219 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ETC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,221 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ENS/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,224 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for EGLD/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,226 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for FIL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,228 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for FLM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,231 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for GRT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,234 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for GALA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,236 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for HBAR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,238 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for HOT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,241 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for IOTX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,243 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ICX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,245 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ICP/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,247 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for IOTA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,251 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for IOST/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,253 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for KAVA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,256 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for KNC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,258 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for KSM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,260 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for LRC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,373 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for MANA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,375 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for MTL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,378 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for NEO/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,380 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ONT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,383 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for OGN/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,386 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ONE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,388 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for PEOPLE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,391 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for RLC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,393 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for RUNE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,396 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for RVN/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,398 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for RSR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,402 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ROSE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,404 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SNX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,406 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SAND/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,446 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SUSHI/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,450 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SKL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,453 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SXP/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,455 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for STORJ/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,458 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for TRX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,460 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for TRB/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,463 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for TLM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,466 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for THETA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,505 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for VET/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,507 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for YFI/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,510 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ZIL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,512 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ZEN/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,514 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ZRX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,517 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ZEC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,557 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for XLM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,559 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for XTZ/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,562 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for XMR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,564 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for QTUM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,567 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for 1INCH/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:30,570 - freqtrade.optimize.backtesting - INFO - Loading data from 2025-01-01 00:00:00 up to 2025-04-29 09:05:00 (118 days).
2025-06-02 14:57:30,576 - freqtrade.data.history.datahandlers.idatahandler - WARNING - AAVE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,588 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ALICE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,599 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ARPA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,611 - freqtrade.data.history.datahandlers.idatahandler - WARNING - AVAX/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:30,623 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ATOM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,634 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ANKR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,647 - freqtrade.data.history.datahandlers.idatahandler - WARNING - AXS/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,659 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ADA/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:30,671 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ALGO/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,682 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BAND/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,693 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BEL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,715 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BTC/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:30,727 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BAT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,740 - freqtrade.data.history.datahandlers.idatahandler - WARNING - CHR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,752 - freqtrade.data.history.datahandlers.idatahandler - WARNING - C98/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,763 - freqtrade.data.history.datahandlers.idatahandler - WARNING - COTI/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,775 - freqtrade.data.history.datahandlers.idatahandler - WARNING - CHZ/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,786 - freqtrade.data.history.datahandlers.idatahandler - WARNING - COMP/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,797 - freqtrade.data.history.datahandlers.idatahandler - WARNING - CRV/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,808 - freqtrade.data.history.datahandlers.idatahandler - WARNING - CELO/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,820 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DUSK/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,831 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DOGE/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:30,844 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DENT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,856 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DASH/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,868 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DOT/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:30,878 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DYDX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,890 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ENJ/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,902 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ETH/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:30,913 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ETC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,925 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ENS/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,937 - freqtrade.data.history.datahandlers.idatahandler - WARNING - EGLD/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,948 - freqtrade.data.history.datahandlers.idatahandler - WARNING - FIL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,959 - freqtrade.data.history.datahandlers.idatahandler - WARNING - FLM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,972 - freqtrade.data.history.datahandlers.idatahandler - WARNING - GRT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,984 - freqtrade.data.history.datahandlers.idatahandler - WARNING - GALA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:30,994 - freqtrade.data.history.datahandlers.idatahandler - WARNING - HBAR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,006 - freqtrade.data.history.datahandlers.idatahandler - WARNING - HOT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,018 - freqtrade.data.history.datahandlers.idatahandler - WARNING - IOTX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,030 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ICX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,042 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ICP/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,054 - freqtrade.data.history.datahandlers.idatahandler - WARNING - IOTA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,065 - freqtrade.data.history.datahandlers.idatahandler - WARNING - IOST/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,076 - freqtrade.data.history.datahandlers.idatahandler - WARNING - KAVA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,088 - freqtrade.data.history.datahandlers.idatahandler - WARNING - KNC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,099 - freqtrade.data.history.datahandlers.idatahandler - WARNING - KSM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,110 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LRC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,122 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LTC/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:31,134 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LINK/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:31,144 - freqtrade.data.history.datahandlers.idatahandler - WARNING - NEAR/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:31,156 - freqtrade.data.history.datahandlers.idatahandler - WARNING - MANA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,171 - freqtrade.data.history.datahandlers.idatahandler - WARNING - MTL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,183 - freqtrade.data.history.datahandlers.idatahandler - WARNING - NEO/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,194 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ONT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,205 - freqtrade.data.history.datahandlers.idatahandler - WARNING - OGN/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,216 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ONE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,228 - freqtrade.data.history.datahandlers.idatahandler - WARNING - PEOPLE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,239 - freqtrade.data.history.datahandlers.idatahandler - WARNING - RLC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,250 - freqtrade.data.history.datahandlers.idatahandler - WARNING - RUNE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,260 - freqtrade.data.history.datahandlers.idatahandler - WARNING - RVN/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,271 - freqtrade.data.history.datahandlers.idatahandler - WARNING - RSR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,283 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ROSE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,294 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SNX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,307 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SAND/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,321 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SOL/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:31,335 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SUSHI/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,348 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SKL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,361 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SXP/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,372 - freqtrade.data.history.datahandlers.idatahandler - WARNING - STORJ/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,384 - freqtrade.data.history.datahandlers.idatahandler - WARNING - TRX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,395 - freqtrade.data.history.datahandlers.idatahandler - WARNING - TRB/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,407 - freqtrade.data.history.datahandlers.idatahandler - WARNING - TLM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,419 - freqtrade.data.history.datahandlers.idatahandler - WARNING - THETA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,431 - freqtrade.data.history.datahandlers.idatahandler - WARNING - UNI/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:31,443 - freqtrade.data.history.datahandlers.idatahandler - WARNING - VET/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,455 - freqtrade.data.history.datahandlers.idatahandler - WARNING - YFI/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,466 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ZIL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,477 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ZEN/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,489 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ZRX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,500 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ZEC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,511 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XRP/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:31,523 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XLM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,535 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XTZ/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,546 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XMR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,557 - freqtrade.data.history.datahandlers.idatahandler - WARNING - QTUM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,570 - freqtrade.data.history.datahandlers.idatahandler - WARNING - 1INCH/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:31,615 - freqtrade.data.history.datahandlers.idatahandler - WARNING - AVAX/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:31,659 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ADA/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:31,735 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BTC/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:31,855 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DOGE/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:31,888 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DOT/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:31,923 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ETH/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:32,167 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LTC/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:32,179 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LINK/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:32,191 - freqtrade.data.history.datahandlers.idatahandler - WARNING - NEAR/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:32,363 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SOL/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:32,487 - freqtrade.data.history.datahandlers.idatahandler - WARNING - UNI/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:32,569 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XRP/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:35,096 - freqtrade.optimize.backtesting - INFO - Dataload complete. Calculating indicators
2025-06-02 14:57:35,098 - freqtrade.optimize.backtesting - WARNING - Backtest result caching disabled due to use of open-ended timerange.
2025-06-02 14:57:35,099 - freqtrade.optimize.backtesting - INFO - Running backtesting for Strategy UltimateHighProbFutures
2025-06-02 14:57:35,100 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-06-02 14:57:35,101 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-06-02 14:57:35,101 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
/freqtrade/user_data/strategies/ultimate_futures_strategy.py:163: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  ].fillna(method='ffill')
/freqtrade/user_data/strategies/ultimate_futures_strategy.py:167: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  ].fillna(method='ffill')
2025-06-02 14:57:35,124 - freqtrade - ERROR - Fatal exception!
Traceback (most recent call last):
  File "/freqtrade/freqtrade/main.py", line 47, in main
    return_code = args["func"](args)
                  ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/commands/optimize_commands.py", line 61, in start_backtesting
    backtesting.start()
  File "/freqtrade/freqtrade/optimize/backtesting.py", line 1806, in start
    min_date, max_date = self.backtest_one_strategy(strat, data, timerange)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/optimize/backtesting.py", line 1716, in backtest_one_strategy
    preprocessed = self.strategy.advise_all_indicators(data)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/strategy/interface.py", line 1705, in advise_all_indicators
    pair: self.advise_indicators(pair_data.copy(), {"pair": pair}).copy()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/strategy/interface.py", line 1760, in advise_indicators
    dataframe = self.populate_indicators(dataframe, metadata)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/user_data/strategies/ultimate_futures_strategy.py", line 181, in populate_indicators
    bb = ta.BBANDS(dataframe, timeperiod=20, nbdevup=2, nbdevdn=2)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "talib/_abstract.pxi", line 461, in talib._ta_lib.Function.__call__
  File "talib/_abstract.pxi", line 311, in talib._ta_lib.Function.set_function_args
  File "talib/_abstract.pxi", line 530, in talib._ta_lib.Function.__check_opt_input_value
TypeError: Invalid parameter value for nbdevup (expected float, got int)

