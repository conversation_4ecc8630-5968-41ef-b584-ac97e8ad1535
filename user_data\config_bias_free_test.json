{"max_open_trades": 30, "stake_currency": "USDT", "stake_amount": 50, "tradable_balance_ratio": 1.0, "fiat_display_currency": "USD", "timeframe": "15m", "dry_run": true, "cancel_open_orders_on_exit": false, "trading_mode": "futures", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "rlhrEBqtMflMnyFbZhPbzOY4ZnEgNWWS0QdtDfxkGEjDzZ3jlOFGN3FHLvTtG3p4", "secret": "zWB5NIGnSXLtNCJaIKqnIFFrBypTh6Gi8SYJI5DIg8wTe4kqBpIG4QTxJvnSwv2x", "ccxt_config": {"enableRateLimit": true, "options": {"defaultType": "future"}}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 200, "options": {"defaultType": "future"}}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT", "ADA/USDT:USDT", "DOT/USDT:USDT", "LINK/USDT:USDT", "LTC/USDT:USDT", "XRP/USDT:USDT", "AVAX/USDT:USDT", "SOL/USDT:USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "edge": {"enabled": false, "process_throttle_secs": 3600, "calculate_since_number_of_days": 7, "allowed_risk": 0.01, "stoploss_range_min": -0.01, "stoploss_range_max": -0.1, "stoploss_range_step": -0.01, "minimum_winrate": 0.6, "minimum_expectancy": 0.2, "min_trade_number": 10, "max_trade_duration_minute": 1440, "remove_pumps": false}, "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": false, "listen_ip_address": "0.0.0.0", "listen_port": 8083, "verbosity": "error", "jwt_secret_key": "something_secret", "CORS_origins": ["*"], "username": "freqtrader", "password": "trading123"}, "bot_name": "BIAS_FREE_TEST", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}}