from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame
import numpy as np
from freqtrade.persistence import Trade
from datetime import datetime
import freqtrade.vendor.qtpylib.indicators as qtpylib
import talib.abstract as ta

class Pivot7Enhanced(IStrategy):
    # Risk Management - More conservative for consistent profits
    stoploss = -0.03  # Tighter stop loss
    can_short = True
    
    # Dynamic trailing stop
    trailing_stop = True
    trailing_stop_positive = 0.005  # Start trailing at 0.5% profit
    trailing_stop_positive_offset = 0.01  # Trail 1% behind peak
    
    timeframe = '15m'
    
    # ROI ladder for profit taking
    minimal_roi = {
        "0": 0.08,    # 8% max profit
        "15": 0.04,   # 4% after 15 minutes
        "30": 0.02,   # 2% after 30 minutes
        "60": 0.01,   # 1% after 1 hour
        "120": 0.005  # 0.5% after 2 hours
    }
    
    # Adaptive parameters
    startup_candle_count: int = 100
    
    # Market condition detection
    trend_strength_threshold = 0.02
    volatility_threshold = 0.015

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Original RSI indicators
        dataframe['rsi_3'] = ta.RSI(dataframe, timeperiod=2)
        dataframe['rsi_6'] = ta.RSI(dataframe, timeperiod=4)
        
        # Additional RSI for trend strength
        dataframe['rsi_14'] = ta.RSI(dataframe, timeperiod=14)
        
        # Pivot calculations (bias-free)
        length = 25
        dataframe['pivot_high'] = dataframe['high'].rolling(window=length, min_periods=1).max().shift(length//2)
        dataframe['pivot_high'] = dataframe['pivot_high'].where(
            dataframe['high'].shift(length//2) == dataframe['pivot_high'], np.nan
        )
        
        dataframe['pivot_low'] = dataframe['low'].rolling(window=length, min_periods=1).min().shift(length//2)
        dataframe['pivot_low'] = dataframe['pivot_low'].where(
            dataframe['low'].shift(length//2) == dataframe['pivot_low'], np.nan
        )
        
        dataframe['high_swing'] = dataframe['pivot_high'].fillna(method='ffill')
        dataframe['low_swing'] = dataframe['pivot_low'].fillna(method='ffill')
        
        # Market condition indicators
        dataframe['ema_9'] = ta.EMA(dataframe, timeperiod=9)
        dataframe['ema_21'] = ta.EMA(dataframe, timeperiod=21)
        dataframe['ema_50'] = ta.EMA(dataframe, timeperiod=50)
        
        # Volatility indicators
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)
        dataframe['bb_upper'], dataframe['bb_middle'], dataframe['bb_lower'] = ta.BBANDS(dataframe, timeperiod=20)
        
        # Volume indicators
        dataframe['volume_sma'] = dataframe['volume'].rolling(window=20).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        
        # Market regime detection
        dataframe['trend_strength'] = abs(dataframe['ema_9'] - dataframe['ema_21']) / dataframe['close']
        dataframe['volatility'] = dataframe['atr'] / dataframe['close']
        
        # Support/Resistance levels
        dataframe['support'] = dataframe['low'].rolling(window=20, min_periods=1).min()
        dataframe['resistance'] = dataframe['high'].rolling(window=20, min_periods=1).max()
        
        # Momentum indicators
        dataframe['macd'], dataframe['macdsignal'], dataframe['macdhist'] = ta.MACD(dataframe)
        dataframe['stoch'] = ta.STOCH(dataframe)['slowk']
        
        # Market structure
        dataframe['higher_high'] = (dataframe['high'] > dataframe['high'].shift(1)) & (dataframe['high'].shift(1) > dataframe['high'].shift(2))
        dataframe['lower_low'] = (dataframe['low'] < dataframe['low'].shift(1)) & (dataframe['low'].shift(1) < dataframe['low'].shift(2))
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0

        # Enhanced long conditions with multiple confirmations
        long_conditions = (
            # Original RSI cross
            qtpylib.crossed_above(dataframe['rsi_3'], dataframe['rsi_6']) &
            
            # Swing level condition
            (dataframe['low_swing'].notna()) &
            (dataframe['close'] > dataframe['low_swing']) &
            
            # Trend alignment
            (dataframe['ema_9'] > dataframe['ema_21']) &
            (dataframe['close'] > dataframe['ema_9']) &
            
            # RSI not overbought
            (dataframe['rsi_14'] < 70) &
            (dataframe['rsi_14'] > 30) &
            
            # Volume confirmation
            (dataframe['volume_ratio'] > 0.8) &
            
            # Volatility filter (avoid low volatility periods)
            (dataframe['volatility'] > 0.005) &
            
            # Price action confirmation
            (dataframe['close'] > dataframe['open']) &
            
            # MACD confirmation
            (dataframe['macd'] > dataframe['macdsignal']) &
            
            # Not at resistance
            (dataframe['close'] < dataframe['resistance'] * 0.99)
        )

        # Enhanced short conditions
        short_conditions = (
            # Original RSI cross
            qtpylib.crossed_below(dataframe['rsi_3'], dataframe['rsi_6']) &
            
            # Swing level condition
            (dataframe['high_swing'].notna()) &
            (dataframe['close'] < dataframe['high_swing']) &
            
            # Trend alignment
            (dataframe['ema_9'] < dataframe['ema_21']) &
            (dataframe['close'] < dataframe['ema_9']) &
            
            # RSI not oversold
            (dataframe['rsi_14'] > 30) &
            (dataframe['rsi_14'] < 70) &
            
            # Volume confirmation
            (dataframe['volume_ratio'] > 0.8) &
            
            # Volatility filter
            (dataframe['volatility'] > 0.005) &
            
            # Price action confirmation
            (dataframe['close'] < dataframe['open']) &
            
            # MACD confirmation
            (dataframe['macd'] < dataframe['macdsignal']) &
            
            # Not at support
            (dataframe['close'] > dataframe['support'] * 1.01)
        )

        dataframe.loc[long_conditions, 'enter_long'] = 1
        dataframe.loc[short_conditions, 'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0

        # Long exit conditions
        long_exit_conditions = (
            (qtpylib.crossed_below(dataframe['rsi_3'], dataframe['rsi_6'])) |
            (dataframe['rsi_14'] > 75) |  # Overbought exit
            (dataframe['close'] < dataframe['ema_9']) |  # Trend reversal
            (qtpylib.crossed_below(dataframe['macd'], dataframe['macdsignal']))  # MACD bearish cross
        )

        # Short exit conditions
        short_exit_conditions = (
            (qtpylib.crossed_above(dataframe['rsi_3'], dataframe['rsi_6'])) |
            (dataframe['rsi_14'] < 25) |  # Oversold exit
            (dataframe['close'] > dataframe['ema_9']) |  # Trend reversal
            (qtpylib.crossed_above(dataframe['macd'], dataframe['macdsignal']))  # MACD bullish cross
        )

        dataframe.loc[long_exit_conditions, 'exit_long'] = 1
        dataframe.loc[short_exit_conditions, 'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """Adaptive stop loss based on volatility and time"""
        
        # Get the dataframe for volatility calculation
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        if len(dataframe) == 0:
            return self.stoploss
            
        current_atr = dataframe['atr'].iloc[-1]
        current_close = dataframe['close'].iloc[-1]
        
        # Calculate ATR-based stop loss
        atr_multiplier = 2.0
        atr_stop_distance = (current_atr * atr_multiplier) / current_close
        
        # Minimum stop loss
        min_stop = -0.02
        
        # Adaptive stop based on profit
        if current_profit > 0.02:  # If profit > 2%
            # Tighten stop loss
            adaptive_stop = max(-0.01, -atr_stop_distance * 0.5)
        elif current_profit > 0.01:  # If profit > 1%
            adaptive_stop = max(-0.015, -atr_stop_distance * 0.75)
        else:
            adaptive_stop = max(min_stop, -atr_stop_distance)
        
        return adaptive_stop

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:
        """Adaptive leverage based on market conditions"""
        
        # Get current market volatility
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        if len(dataframe) == 0:
            return 5.0  # Conservative default
            
        current_volatility = dataframe['volatility'].iloc[-1]
        
        # Adjust leverage based on volatility
        if current_volatility > 0.025:  # High volatility
            return min(3.0, max_leverage)
        elif current_volatility > 0.015:  # Medium volatility
            return min(5.0, max_leverage)
        else:  # Low volatility
            return min(10.0, max_leverage)

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float,
                           rate: float, time_in_force: str, current_time: datetime,
                           entry_tag: str, side: str, **kwargs) -> bool:
        """Final check before entering trade"""
        
        # Get current market data
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        
        if len(dataframe) == 0:
            return False
            
        # Check if market conditions are still favorable
        current_volatility = dataframe['volatility'].iloc[-1]
        current_volume_ratio = dataframe['volume_ratio'].iloc[-1]
        
        # Don't enter during very low volatility or volume
        if current_volatility < 0.003 or current_volume_ratio < 0.5:
            return False
            
        return True