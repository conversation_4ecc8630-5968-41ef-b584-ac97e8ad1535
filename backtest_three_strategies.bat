@echo off
echo ========================================
echo Backtesting Three Strategies Comparison
echo From January 1, 2025 to Now
echo Starting Balance: 100 USDT
echo Stake Amount: 5 USDT per trade
echo ========================================

REM Set common parameters
set TIMERANGE=20250101-
set CONFIG=user_data/config_backtest_comparison.json
set TIMEFRAME=5m

REM Create results directory if it doesn't exist
if not exist "backtest_results" mkdir backtest_results

echo.
echo ===== Testing UltimateHighProbFutures Strategy =====
echo Strategy: UltimateHighProbFutures
echo Timeframe: %TIMEFRAME%
echo Timerange: %TIMERANGE%
echo Config: %CONFIG%
echo.

docker compose run --rm freqtrade backtesting --strategy UltimateHighProbFutures --config %CONFIG% --timerange=%TIMERANGE% --timeframe=%TIMEFRAME% --starting-balance 100 > backtest_results\UltimateHighProbFutures_results.txt 2>&1

echo Results saved to backtest_results\UltimateHighProbFutures_results.txt
echo.

echo ===== Testing VolatimImproved Strategy =====
echo Strategy: VolatimImproved
echo Timeframe: %TIMEFRAME%
echo Timerange: %TIMERANGE%
echo Config: %CONFIG%
echo.

docker compose run --rm freqtrade backtesting --strategy VolatimImproved --config %CONFIG% --timerange=%TIMERANGE% --timeframe=%TIMEFRAME% --starting-balance 100 > backtest_results\VolatimImproved_results.txt 2>&1

echo Results saved to backtest_results\VolatimImproved_results.txt
echo.

echo ===== Testing Pivot7Improved Strategy =====
echo Strategy: Pivot7Improved
echo Timeframe: %TIMEFRAME%
echo Timerange: %TIMERANGE%
echo Config: %CONFIG%
echo.

docker compose run --rm freqtrade backtesting --strategy Pivot7Improved --config %CONFIG% --timerange=%TIMERANGE% --timeframe=%TIMEFRAME% --starting-balance 100 > backtest_results\Pivot7Improved_results.txt 2>&1

echo Results saved to backtest_results\Pivot7Improved_results.txt
echo.

echo ========================================
echo All backtests completed!
echo ========================================
echo.
echo Results saved in backtest_results folder:
echo - UltimateHighProbFutures_results.txt
echo - VolatimImproved_results.txt  
echo - Pivot7Improved_results.txt
echo.
echo You can review the results to compare:
echo - Total Return
echo - Win Rate
echo - Profit Factor
echo - Maximum Drawdown
echo - Sharpe Ratio
echo - Number of Trades
echo.
echo Your current running container was NOT affected.
echo ========================================

pause
