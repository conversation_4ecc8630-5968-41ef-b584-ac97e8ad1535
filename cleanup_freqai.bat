@echo off
echo Cleaning up FreqAI data to fix corruption issues...

REM Create backup directory for FreqAI models
mkdir user_data\models_backup 2>nul

REM Move existing models to backup
echo Moving existing FreqAI models to backup folder...
docker compose run --rm --entrypoint sh freqtrade -c "mkdir -p /freqtrade/user_data/models_backup && cp -r /freqtrade/user_data/models/* /freqtrade/user_data/models_backup/ 2>/dev/null || true"

REM Remove corrupted models
echo Removing corrupted FreqAI models...
docker compose run --rm --entrypoint sh freqtrade -c "rm -rf /freqtrade/user_data/models/* 2>/dev/null || true"

echo FreqAI data cleanup completed.
echo Your original models have been backed up to user_data/models_backup
