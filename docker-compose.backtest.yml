---
services:
  freqtrade:
    image: freqtradeorg/freqtrade:develop_plot
    container_name: freqtrade_backtest
    volumes:
      - "./user_data:/freqtrade/user_data"
    ports:
      - "127.0.0.1:8080:8080"
    command: >
      webserver
      --config /freqtrade/user_data/config_backtest.json
      --strategy HighLeverageTrendFollowing
      --db-url sqlite:////freqtrade/user_data/tradesv3_backtest.sqlite
      --no-telegram
      --enable-openapi
      --enable-ui-frame
