{"freqai": {"enabled": true, "purge_old_models": true, "train_period_days": 30, "backtest_period_days": 7, "identifier": "hlf_ai", "feature_parameters": {"include_timeframes": ["15m", "1h"], "include_corr_pairlist": [], "label_period_candles": 10, "include_shifted_candles": 3, "DI_threshold": 0.75, "weight_factor": 0.9, "principal_component_analysis": false, "use_SVM_to_remove_outliers": false, "stratify_training_data": 0, "indicator_periods_candles": [10, 20, 30], "plot_feature_importances": 0, "shuffle_after_split": false, "buffer_train_data_candles": 0}, "data_split_parameters": {"test_size": 0.2, "random_state": 42, "shuffle": true}, "model_training_parameters": {"n_estimators": 100, "learning_rate": 0.1, "max_depth": 7, "gamma": 0, "subsample": 0.8, "colsample_bytree": 0.8, "use_early_stopping": true, "early_stopping_rounds": 50, "eval_metric": "logloss", "verbosity": 0}, "freqaimodel": "LightGBMClassifier", "write_metrics_to_disk": false, "conv_width": 1, "live_retrain_hours": 0, "expiration_hours": 0, "save_backtest_models": false, "activate_tensorboard": true, "wait_for_training_iteration_on_reload": true, "continual_learning": false, "keras": false}, "timeframe": "15m", "stake_amount": 100, "stake_currency": "USDT", "max_open_trades": 30, "pairs": ["AAVE/USDT:USDT", "ALICE/USDT:USDT", "ARPA/USDT:USDT", "AVAX/USDT:USDT", "ATOM/USDT:USDT", "ANKR/USDT:USDT", "AXS/USDT:USDT", "ADA/USDT:USDT", "ALGO/USDT:USDT", "BAND/USDT:USDT", "BEL/USDT:USDT", "BNB/USDT:USDT", "BTC/USDT:USDT", "BAT/USDT:USDT", "CHR/USDT:USDT", "C98/USDT:USDT", "COTI/USDT:USDT", "CHZ/USDT:USDT", "COMP/USDT:USDT", "CRV/USDT:USDT", "CELO/USDT:USDT", "DUSK/USDT:USDT", "DOGE/USDT:USDT", "DENT/USDT:USDT", "DASH/USDT:USDT", "DOT/USDT:USDT", "DYDX/USDT:USDT", "ENJ/USDT:USDT", "EOS/USDT:USDT", "ETH/USDT:USDT", "ETC/USDT:USDT", "ENS/USDT:USDT", "EGLD/USDT:USDT", "FIL/USDT:USDT", "FLM/USDT:USDT", "GRT/USDT:USDT", "GALA/USDT:USDT", "HBAR/USDT:USDT", "HOT/USDT:USDT", "IOTX/USDT:USDT", "ICX/USDT:USDT", "ICP/USDT:USDT", "IOTA/USDT:USDT", "IOST/USDT:USDT", "KAVA/USDT:USDT", "KNC/USDT:USDT", "KSM/USDT:USDT", "LRC/USDT:USDT", "LTC/USDT:USDT", "LINK/USDT:USDT", "NEAR/USDT:USDT", "MANA/USDT:USDT", "MTL/USDT:USDT", "NEO/USDT:USDT", "ONT/USDT:USDT", "OGN/USDT:USDT", "ONE/USDT:USDT", "PEOPLE/USDT:USDT", "RLC/USDT:USDT", "RUNE/USDT:USDT", "RVN/USDT:USDT", "RSR/USDT:USDT", "ROSE/USDT:USDT", "SNX/USDT:USDT", "SAND/USDT:USDT", "SOL/USDT:USDT", "SUSHI/USDT:USDT", "SKL/USDT:USDT", "SXP/USDT:USDT", "STORJ/USDT:USDT", "TRX/USDT:USDT", "TRB/USDT:USDT", "TLM/USDT:USDT", "THETA/USDT:USDT", "UNI/USDT:USDT", "VET/USDT:USDT", "YFI/USDT:USDT", "ZIL/USDT:USDT", "ZEN/USDT:USDT", "ZRX/USDT:USDT", "ZEC/USDT:USDT", "XRP/USDT:USDT", "XLM/USDT:USDT", "XTZ/USDT:USDT", "XMR/USDT:USDT", "QTUM/USDT:USDT", "1INCH/USDT:USDT"]}