2025-06-02 14:57:37,388 - freqtrade - INFO - freqtrade docker-2025.5-dev-f3d03dd2
2025-06-02 14:57:37,594 - numexpr.utils - INFO - NumExpr defaulting to 12 threads.
2025-06-02 14:57:38,690 - freqtrade.configuration.load_config - INFO - Using config: user_data/config_backtest_comparison.json ...
2025-06-02 14:57:38,694 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 14:57:38,695 - root - INFO - Logfile configured
2025-06-02 14:57:38,695 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 14:57:38,696 - freqtrade.configuration.configuration - INFO - Parameter -i/--timeframe detected ... Using timeframe: 5m ...
2025-06-02 14:57:38,696 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 20 ...
2025-06-02 14:57:38,697 - freqtrade.configuration.configuration - INFO - Parameter --dry-run-wallet detected, overriding dry_run_wallet to: 100.0 ...
2025-06-02 14:57:38,697 - freqtrade.configuration.configuration - INFO - Parameter --timerange detected: 20250101- ...
2025-06-02 14:57:40,998 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 14:57:41,000 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-06-02 14:57:41,000 - freqtrade.configuration.configuration - INFO - Overriding timeframe with Command line argument
2025-06-02 14:57:41,001 - freqtrade.configuration.configuration - INFO - Parameter --cache=day detected ...
2025-06-02 14:57:41,001 - freqtrade.configuration.configuration - INFO - Filter trades by timerange: 20250101-
2025-06-02 14:57:41,003 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 14:57:41,013 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-06-02 14:57:41,013 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 14:57:41,014 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 14:57:41,017 - freqtrade.commands.optimize_commands - INFO - Starting freqtrade in Backtesting mode
2025-06-02 14:57:41,018 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-06-02 14:57:41,018 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.82
2025-06-02 14:57:41,019 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'future'}, 'enableRateLimit': True}
2025-06-02 14:57:41,027 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'future'}, 'enableRateLimit': True, 'rateLimit': 200}
2025-06-02 14:57:41,038 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-06-02 14:57:43,866 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2025-06-02 14:57:43,946 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/AdvancedFuturesAIPower.py due to 'No module named 
'freqtrade.freqai.FreqaiStrategy''
2025-06-02 14:57:44,108 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/GodStra.py due to 'No module named 'ta''
2025-06-02 14:57:44,113 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/Heracles.py due to 'No module named 'ta''
2025-06-02 14:57:44,402 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/UltraHFTMultiStrategy_part2.py due to 'unexpected indent 
(UltraHFTMultiStrategy_part2.py, line 1)'
2025-06-02 14:57:44,407 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/UltraHFTMultiStrategy_part3.py due to 'unexpected indent 
(UltraHFTMultiStrategy_part3.py, line 1)'
2025-06-02 14:57:44,421 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/UltraProfitPredator.py due to 'name 'nn' is not defined'
2025-06-02 14:57:44,438 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy VolatimImproved from '/freqtrade/user_data/strategies/volatim_improved.py'...
2025-06-02 14:57:44,439 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-02 14:57:44,440 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-02 14:57:44,440 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-06-02 14:57:44,440 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: 5.
2025-06-02 14:57:44,441 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 
'minutes'}.
2025-06-02 14:57:44,441 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 20.
2025-06-02 14:57:44,442 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.12, '15': 0.08, '30': 0.05, '60': 0.03, '120': 0.015}
2025-06-02 14:57:44,442 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-02 14:57:44,443 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.08
2025-06-02 14:57:44,443 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-06-02 14:57:44,443 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.015
2025-06-02 14:57:44,444 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.025
2025-06-02 14:57:44,444 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-02 14:57:44,444 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-02 14:57:44,445 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-02 14:57:44,445 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 
'stoploss_on_exchange_interval': 60}
2025-06-02 14:57:44,446 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-02 14:57:44,446 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-06-02 14:57:44,447 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: 5
2025-06-02 14:57:44,447 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 0
2025-06-02 14:57:44,448 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-02 14:57:44,448 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-02 14:57:44,449 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-02 14:57:44,449 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-06-02 14:57:44,450 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-02 14:57:44,450 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-02 14:57:44,451 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-02 14:57:44,451 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-02 14:57:44,451 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-02 14:57:44,452 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 20
2025-06-02 14:57:44,452 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 14:57:44,470 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-06-02 14:57:44,526 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair AUDIO/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:44,526 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair BTS/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:44,527 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring BLZ/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:44,527 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair EOS/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:44,528 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring FTM/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:44,528 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring KLAY/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:44,528 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair LUNA/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:44,529 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring LINA/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:44,529 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair MATIC/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:44,530 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring OMG/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:44,530 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring OCEAN/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:44,531 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring REEF/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:44,531 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair SRM/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:44,532 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair TOMO/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:44,532 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring UNFI/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:44,533 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring WAVES/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:44,533 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring XEM/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:44,537 - freqtrade.optimize.backtesting - INFO - Using fee 0.0500% - worst case fee from exchange (lowest tier).
2025-06-02 14:57:44,553 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for AAVE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,555 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ALICE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,558 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ARPA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,654 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ANKR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,657 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for AXS/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,699 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ALGO/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,702 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for BAND/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,704 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for BEL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,781 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for BAT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,784 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for CHR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,786 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for C98/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,789 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for COTI/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,791 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for CHZ/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,793 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for COMP/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,795 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for CRV/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,798 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for CELO/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,801 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for DUSK/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,841 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for DENT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,844 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for DASH/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,884 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for DYDX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,887 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ENJ/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,928 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ETC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,930 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ENS/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,933 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for EGLD/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,936 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for FIL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,938 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for FLM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,940 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for GRT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,943 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for GALA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,945 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for HBAR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,947 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for HOT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,950 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for IOTX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,953 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ICX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,955 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ICP/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,958 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for IOTA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,960 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for IOST/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,962 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for KAVA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,964 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for KNC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,967 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for KSM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:44,970 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for LRC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,084 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for MANA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,087 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for MTL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,090 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for NEO/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,092 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ONT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,094 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for OGN/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,097 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ONE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,100 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for PEOPLE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,102 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for RLC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,105 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for RUNE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,107 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for RVN/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,109 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for RSR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,112 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ROSE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,115 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SNX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,118 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SAND/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,157 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SUSHI/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,159 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SKL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,162 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SXP/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,164 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for STORJ/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,167 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for TRX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,170 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for TRB/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,172 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for TLM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,174 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for THETA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,213 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for VET/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,217 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for YFI/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,219 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ZIL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,222 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ZEN/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,224 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ZRX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,226 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ZEC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,265 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for XLM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,268 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for XTZ/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,270 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for XMR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,272 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for QTUM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,275 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for 1INCH/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:45,277 - freqtrade.optimize.backtesting - INFO - Loading data from 2025-01-01 00:00:00 up to 2025-04-29 09:05:00 (118 days).
2025-06-02 14:57:45,285 - freqtrade.data.history.datahandlers.idatahandler - WARNING - AAVE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,296 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ALICE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,307 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ARPA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,320 - freqtrade.data.history.datahandlers.idatahandler - WARNING - AVAX/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:45,332 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ATOM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,343 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ANKR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,354 - freqtrade.data.history.datahandlers.idatahandler - WARNING - AXS/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,366 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ADA/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:45,377 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ALGO/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,389 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BAND/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,400 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BEL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,424 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BTC/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:45,436 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BAT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,448 - freqtrade.data.history.datahandlers.idatahandler - WARNING - CHR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,460 - freqtrade.data.history.datahandlers.idatahandler - WARNING - C98/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,471 - freqtrade.data.history.datahandlers.idatahandler - WARNING - COTI/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,483 - freqtrade.data.history.datahandlers.idatahandler - WARNING - CHZ/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,494 - freqtrade.data.history.datahandlers.idatahandler - WARNING - COMP/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,506 - freqtrade.data.history.datahandlers.idatahandler - WARNING - CRV/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,518 - freqtrade.data.history.datahandlers.idatahandler - WARNING - CELO/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,529 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DUSK/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,541 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DOGE/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:45,553 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DENT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,565 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DASH/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,576 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DOT/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:45,588 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DYDX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,599 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ENJ/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,611 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ETH/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:45,622 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ETC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,633 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ENS/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,645 - freqtrade.data.history.datahandlers.idatahandler - WARNING - EGLD/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,657 - freqtrade.data.history.datahandlers.idatahandler - WARNING - FIL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,670 - freqtrade.data.history.datahandlers.idatahandler - WARNING - FLM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,681 - freqtrade.data.history.datahandlers.idatahandler - WARNING - GRT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,693 - freqtrade.data.history.datahandlers.idatahandler - WARNING - GALA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,704 - freqtrade.data.history.datahandlers.idatahandler - WARNING - HBAR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,717 - freqtrade.data.history.datahandlers.idatahandler - WARNING - HOT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,728 - freqtrade.data.history.datahandlers.idatahandler - WARNING - IOTX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,739 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ICX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,751 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ICP/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,763 - freqtrade.data.history.datahandlers.idatahandler - WARNING - IOTA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,775 - freqtrade.data.history.datahandlers.idatahandler - WARNING - IOST/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,786 - freqtrade.data.history.datahandlers.idatahandler - WARNING - KAVA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,797 - freqtrade.data.history.datahandlers.idatahandler - WARNING - KNC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,811 - freqtrade.data.history.datahandlers.idatahandler - WARNING - KSM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,823 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LRC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,835 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LTC/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:45,846 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LINK/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:45,857 - freqtrade.data.history.datahandlers.idatahandler - WARNING - NEAR/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:45,869 - freqtrade.data.history.datahandlers.idatahandler - WARNING - MANA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,880 - freqtrade.data.history.datahandlers.idatahandler - WARNING - MTL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,892 - freqtrade.data.history.datahandlers.idatahandler - WARNING - NEO/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,904 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ONT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,915 - freqtrade.data.history.datahandlers.idatahandler - WARNING - OGN/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,926 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ONE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,938 - freqtrade.data.history.datahandlers.idatahandler - WARNING - PEOPLE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,950 - freqtrade.data.history.datahandlers.idatahandler - WARNING - RLC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,961 - freqtrade.data.history.datahandlers.idatahandler - WARNING - RUNE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,973 - freqtrade.data.history.datahandlers.idatahandler - WARNING - RVN/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,985 - freqtrade.data.history.datahandlers.idatahandler - WARNING - RSR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:45,996 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ROSE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,008 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SNX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,020 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SAND/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,033 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SOL/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:46,045 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SUSHI/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,057 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SKL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,069 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SXP/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,080 - freqtrade.data.history.datahandlers.idatahandler - WARNING - STORJ/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,092 - freqtrade.data.history.datahandlers.idatahandler - WARNING - TRX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,106 - freqtrade.data.history.datahandlers.idatahandler - WARNING - TRB/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,117 - freqtrade.data.history.datahandlers.idatahandler - WARNING - TLM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,129 - freqtrade.data.history.datahandlers.idatahandler - WARNING - THETA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,140 - freqtrade.data.history.datahandlers.idatahandler - WARNING - UNI/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:46,151 - freqtrade.data.history.datahandlers.idatahandler - WARNING - VET/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,162 - freqtrade.data.history.datahandlers.idatahandler - WARNING - YFI/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,173 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ZIL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,185 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ZEN/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,196 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ZRX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,207 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ZEC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,219 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XRP/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:46,231 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XLM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,242 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XTZ/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,254 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XMR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,266 - freqtrade.data.history.datahandlers.idatahandler - WARNING - QTUM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,277 - freqtrade.data.history.datahandlers.idatahandler - WARNING - 1INCH/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:46,326 - freqtrade.data.history.datahandlers.idatahandler - WARNING - AVAX/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:46,377 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ADA/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:46,435 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BTC/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:46,552 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DOGE/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:46,587 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DOT/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:46,622 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ETH/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:46,842 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LTC/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:46,854 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LINK/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:46,866 - freqtrade.data.history.datahandlers.idatahandler - WARNING - NEAR/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:47,047 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SOL/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:47,154 - freqtrade.data.history.datahandlers.idatahandler - WARNING - UNI/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:47,232 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XRP/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:49,837 - freqtrade.optimize.backtesting - INFO - Dataload complete. Calculating indicators
2025-06-02 14:57:49,840 - freqtrade.optimize.backtesting - WARNING - Backtest result caching disabled due to use of open-ended timerange.
2025-06-02 14:57:49,840 - freqtrade.optimize.backtesting - INFO - Running backtesting for Strategy VolatimImproved
2025-06-02 14:57:49,841 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-06-02 14:57:49,842 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-06-02 14:57:49,842 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-06-02 14:57:49,853 - freqtrade - ERROR - Fatal exception!
Traceback (most recent call last):
  File "/freqtrade/freqtrade/main.py", line 47, in main
    return_code = args["func"](args)
                  ^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/commands/optimize_commands.py", line 61, in start_backtesting
    backtesting.start()
  File "/freqtrade/freqtrade/optimize/backtesting.py", line 1806, in start
    min_date, max_date = self.backtest_one_strategy(strat, data, timerange)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/optimize/backtesting.py", line 1716, in backtest_one_strategy
    preprocessed = self.strategy.advise_all_indicators(data)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/strategy/interface.py", line 1705, in advise_all_indicators
    pair: self.advise_indicators(pair_data.copy(), {"pair": pair}).copy()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/strategy/interface.py", line 1760, in advise_indicators
    dataframe = self.populate_indicators(dataframe, metadata)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/user_data/strategies/volatim_improved.py", line 105, in populate_indicators
    bb = ta.BBANDS(dataframe, timeperiod=20, nbdevup=2, nbdevdn=2)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "talib/_abstract.pxi", line 461, in talib._ta_lib.Function.__call__
  File "talib/_abstract.pxi", line 311, in talib._ta_lib.Function.set_function_args
  File "talib/_abstract.pxi", line 530, in talib._ta_lib.Function.__check_opt_input_value
TypeError: Invalid parameter value for nbdevup (expected float, got int)

