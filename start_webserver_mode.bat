@echo off
echo Starting FreqTrade in webserver mode...

REM Stop any running containers first
docker compose down

REM Start FreqTrade in webserver mode - IMPORTANT: No trade mode, just webserver
docker compose run --rm -p 8080:8080 freqtrade webserver --no-telegram

echo.
echo FreqTrade webserver started! Access the UI at http://localhost:8080
echo.
echo You can now run backtests for any strategy through the web interface.
echo Press Ctrl+C to stop the server when done.
