2025-05-25 15:09:29,463 - freqtrade.loggers - INFO - Enabling colorized output.
2025-05-25 15:09:29,464 - root - INFO - Logfile configured
2025-05-25 15:09:29,465 - freqtrade.loggers - INFO - Verbosity set to 0
2025-05-25 15:09:29,465 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-05-25 15:09:29,466 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-05-25 15:09:29,466 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.dryrun.sqlite"
2025-05-25 15:09:29,472 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 30 ...
2025-05-25 15:09:32,203 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-05-25 15:09:32,204 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-05-25 15:09:32,205 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-05-25 15:09:32,217 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-05-25 15:09:32,218 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-05-25 15:09:32,370 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/AdvancedFuturesAIPower.py due to 'No module named 'freqtrade.freqai.FreqaiStrategy''
2025-05-25 15:09:32,543 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/GodStra.py due to 'No module named 'ta''
2025-05-25 15:09:32,548 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/Heracles.py due to 'No module named 'ta''
2025-05-25 15:09:32,709 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy Pivot7 from '/freqtrade/user_data/strategies/Pivot7.py'...
2025-05-25 15:09:32,710 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-05-25 15:09:32,710 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 15m.
2025-05-25 15:09:32,711 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-05-25 15:09:32,712 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: 100.
2025-05-25 15:09:32,712 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-05-25 15:09:32,713 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 30.
2025-05-25 15:09:32,714 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {}
2025-05-25 15:09:32,714 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 15m
2025-05-25 15:09:32,715 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-05-25 15:09:32,715 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-05-25 15:09:32,716 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.1
2025-05-25 15:09:32,716 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.5
2025-05-25 15:09:32,717 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-05-25 15:09:32,717 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-05-25 15:09:32,718 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-05-25 15:09:32,718 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 'stoploss_on_exchange_interval': 60}
2025-05-25 15:09:32,719 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-05-25 15:09:32,719 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-05-25 15:09:32,720 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: 100
2025-05-25 15:09:32,720 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 0
2025-05-25 15:09:32,721 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-05-25 15:09:32,722 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-05-25 15:09:32,722 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-05-25 15:09:32,723 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-05-25 15:09:32,723 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-05-25 15:09:32,724 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-05-25 15:09:32,724 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-05-25 15:09:32,725 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-05-25 15:09:32,725 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-05-25 15:09:32,726 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 30
2025-05-25 15:09:32,726 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-05-25 15:09:32,730 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-05-25 15:09:32,731 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.69
2025-05-25 15:09:32,731 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-05-25 15:09:32,742 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'enableRateLimit': True, 'rateLimit': 200}
2025-05-25 15:09:32,754 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-05-25 15:09:35,810 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2025-05-25 15:09:35,942 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:09:36,418 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-05-25 15:09:36,615 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-05-25 15:09:36,887 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-05-25 15:09:36,890 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-05-25 15:09:36,928 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-05-25 15:09:36,929 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-05-25 15:09:36,997 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair AUDIO/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:09:36,999 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['edge'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info']]
2025-05-25 15:09:37,000 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair BTS/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:09:37,002 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring BLZ/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:09:37,003 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair EOS/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:09:37,004 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring FTM/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:09:37,009 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring KLAY/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:09:37,010 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair LUNA/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:09:37,011 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring LINA/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:09:37,012 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair MATIC/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:09:37,012 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring OMG/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:09:37,013 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring OCEAN/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:09:37,013 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring REEF/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:09:37,014 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair SRM/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:09:37,014 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair TOMO/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:09:37,015 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring UNFI/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:09:37,016 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring WAVES/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:09:37,016 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring XEM/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:09:37,021 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 86 pairs: ['AAVE/USDT:USDT', 'ALICE/USDT:USDT', 'ARPA/USDT:USDT', 'AVAX/USDT:USDT', 'ATOM/USDT:USDT', 'ANKR/USDT:USDT', 'AXS/USDT:USDT', 'ADA/USDT:USDT', 'ALGO/USDT:USDT', 'BAND/USDT:USDT', 'BEL/USDT:USDT', 'BNB/USDT:USDT', 'BTC/USDT:USDT', 'BAT/USDT:USDT', 'CHR/USDT:USDT', 'C98/USDT:USDT', 'COTI/USDT:USDT', 'CHZ/USDT:USDT', 'COMP/USDT:USDT', 'CRV/USDT:USDT', 'CELO/USDT:USDT', 'DUSK/USDT:USDT', 'DOGE/USDT:USDT', 'DENT/USDT:USDT', 'DASH/USDT:USDT', 'DOT/USDT:USDT', 'DYDX/USDT:USDT', 'ENJ/USDT:USDT', 'ETH/USDT:USDT', 'ETC/USDT:USDT', 'ENS/USDT:USDT', 'EGLD/USDT:USDT', 'FIL/USDT:USDT', 'FLM/USDT:USDT', 'GRT/USDT:USDT', 'GALA/USDT:USDT', 'HBAR/USDT:USDT', 'HOT/USDT:USDT', 'IOTX/USDT:USDT', 'ICX/USDT:USDT', 'ICP/USDT:USDT', 'IOTA/USDT:USDT', 'IOST/USDT:USDT', 'KAVA/USDT:USDT', 'KNC/USDT:USDT', 'KSM/USDT:USDT', 'LRC/USDT:USDT', 'LTC/USDT:USDT', 'LINK/USDT:USDT', 'NEAR/USDT:USDT', 'MANA/USDT:USDT', 'MTL/USDT:USDT', 'NEO/USDT:USDT', 'ONT/USDT:USDT', 'OGN/USDT:USDT', 'ONE/USDT:USDT', 'PEOPLE/USDT:USDT', 'RLC/USDT:USDT', 'RUNE/USDT:USDT', 'RVN/USDT:USDT', 'RSR/USDT:USDT', 'ROSE/USDT:USDT', 'SNX/USDT:USDT', 'SAND/USDT:USDT', 'SOL/USDT:USDT', 'SUSHI/USDT:USDT', 'SKL/USDT:USDT', 'SXP/USDT:USDT', 'STORJ/USDT:USDT', 'TRX/USDT:USDT', 'TRB/USDT:USDT', 'TLM/USDT:USDT', 'THETA/USDT:USDT', 'UNI/USDT:USDT', 'VET/USDT:USDT', 'YFI/USDT:USDT', 'ZIL/USDT:USDT', 'ZEN/USDT:USDT', 'ZRX/USDT:USDT', 'ZEC/USDT:USDT', 'XRP/USDT:USDT', 'XLM/USDT:USDT', 'XTZ/USDT:USDT', 'XMR/USDT:USDT', 'QTUM/USDT:USDT', '1INCH/USDT:USDT']
2025-05-25 15:09:37,022 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.09s
2025-05-25 15:09:37,024 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-05-25 15:09:37,025 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-05-25 15:09:37,025 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-05-25 15:09:37,026 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-05-25 15:09:37,026 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-05-25 15:09:37,027 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-05-25 15:09:37,029 - freqtrade.util.migrations.binance_mig - WARNING - Migrating binance futures pairs in database.
2025-05-25 15:09:37,034 - freqtrade.util.migrations.binance_mig - WARNING - Done migrating binance futures pairs in database.
2025-05-25 15:09:37,058 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': 'Dry run is enabled. All trades are simulated.'}
2025-05-25 15:09:37,058 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': '*Exchange:* `binance`\n*Stake per trade:* `100 USDT`\n*Minimum ROI:* `{}`\n*Stoploss:* `-0.5`\n*Position adjustment:* `Off`\n*Timeframe:* `15m`\n*Strategy:* `Pivot7`'}
2025-05-25 15:09:37,060 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDT pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-05-25 15:09:37,485 - telegram.ext.Application - INFO - Application started
2025-05-25 15:10:28,130 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(22b4501f, ('172.18.0.1', 36102))
2025-05-25 15:10:34,179 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(d5e3ed52, ('172.18.0.1', 46270))
2025-05-25 15:11:38,561 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-05-25 15:11:38,562 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-05-25 15:11:38,563 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-05-25 15:11:38,567 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-05-25 15:11:38,568 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-05-25 15:11:38,568 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-05-25 15:11:38,577 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(22b4501f, ('172.18.0.1', 36102))
2025-05-25 15:11:38,578 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(d5e3ed52, ('172.18.0.1', 46270))
2025-05-25 15:11:38,678 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.telegram ...
2025-05-25 15:11:38,931 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-05-25 15:11:38,932 - telegram.ext.Application - INFO - Application.stop() complete
2025-05-25 15:11:47,952 - freqtrade - INFO - SIGINT received, aborting ...
2025-05-25 15:11:48,113 - ccxt.base.exchange - WARNING - binance requires to release all resources with an explicit call to the .close() coroutine. If you are using the exchange instance with async coroutines, add `await exchange.close()` to your code into a place when you're done with the exchange and don't need the exchange instance anymore (at the end of your async coroutine).
2025-05-25 15:11:51,761 - freqtrade.loggers - INFO - Enabling colorized output.
2025-05-25 15:11:51,761 - root - INFO - Logfile configured
2025-05-25 15:11:51,762 - freqtrade.loggers - INFO - Verbosity set to 0
2025-05-25 15:11:51,762 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-05-25 15:11:51,763 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-05-25 15:11:51,763 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.dryrun.sqlite"
2025-05-25 15:11:51,764 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 30 ...
2025-05-25 15:11:54,164 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-05-25 15:11:54,166 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-05-25 15:11:54,167 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-05-25 15:11:54,179 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-05-25 15:11:54,179 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-05-25 15:11:54,263 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/AdvancedFuturesAIPower.py due to 'No module named 'freqtrade.freqai.FreqaiStrategy''
2025-05-25 15:11:54,430 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/GodStra.py due to 'No module named 'ta''
2025-05-25 15:11:54,436 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/Heracles.py due to 'No module named 'ta''
2025-05-25 15:11:54,602 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy Pivot7 from '/freqtrade/user_data/strategies/Pivot7.py'...
2025-05-25 15:11:54,603 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-05-25 15:11:54,604 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 15m.
2025-05-25 15:11:54,604 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-05-25 15:11:54,605 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: 100.
2025-05-25 15:11:54,605 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-05-25 15:11:54,606 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 30.
2025-05-25 15:11:54,607 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {}
2025-05-25 15:11:54,607 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 15m
2025-05-25 15:11:54,607 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-05-25 15:11:54,608 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-05-25 15:11:54,608 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.1
2025-05-25 15:11:54,609 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.5
2025-05-25 15:11:54,609 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-05-25 15:11:54,610 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-05-25 15:11:54,610 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-05-25 15:11:54,611 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 'stoploss_on_exchange_interval': 60}
2025-05-25 15:11:54,611 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-05-25 15:11:54,612 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-05-25 15:11:54,612 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: 100
2025-05-25 15:11:54,613 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 0
2025-05-25 15:11:54,613 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-05-25 15:11:54,614 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-05-25 15:11:54,615 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-05-25 15:11:54,615 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-05-25 15:11:54,616 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-05-25 15:11:54,616 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-05-25 15:11:54,617 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-05-25 15:11:54,617 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-05-25 15:11:54,618 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-05-25 15:11:54,618 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 30
2025-05-25 15:11:54,619 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-05-25 15:11:54,622 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-05-25 15:11:54,623 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.69
2025-05-25 15:11:54,623 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-05-25 15:11:54,633 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'enableRateLimit': True, 'rateLimit': 200}
2025-05-25 15:11:54,644 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-05-25 15:11:58,580 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2025-05-25 15:11:58,607 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:11:59,026 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-05-25 15:11:59,125 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-05-25 15:11:59,351 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-05-25 15:11:59,352 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-05-25 15:11:59,381 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-05-25 15:11:59,382 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-05-25 15:11:59,436 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair AUDIO/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:11:59,437 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['edge'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info']]
2025-05-25 15:11:59,438 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair BTS/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:11:59,440 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring BLZ/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:11:59,440 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair EOS/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:11:59,441 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring FTM/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:11:59,444 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring KLAY/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:11:59,445 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair LUNA/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:11:59,446 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring LINA/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:11:59,446 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair MATIC/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:11:59,447 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring OMG/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:11:59,448 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring OCEAN/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:11:59,449 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring REEF/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:11:59,449 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair SRM/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:11:59,450 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair TOMO/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:11:59,451 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring UNFI/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:11:59,451 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring WAVES/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:11:59,452 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring XEM/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:11:59,456 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 86 pairs: ['AAVE/USDT:USDT', 'ALICE/USDT:USDT', 'ARPA/USDT:USDT', 'AVAX/USDT:USDT', 'ATOM/USDT:USDT', 'ANKR/USDT:USDT', 'AXS/USDT:USDT', 'ADA/USDT:USDT', 'ALGO/USDT:USDT', 'BAND/USDT:USDT', 'BEL/USDT:USDT', 'BNB/USDT:USDT', 'BTC/USDT:USDT', 'BAT/USDT:USDT', 'CHR/USDT:USDT', 'C98/USDT:USDT', 'COTI/USDT:USDT', 'CHZ/USDT:USDT', 'COMP/USDT:USDT', 'CRV/USDT:USDT', 'CELO/USDT:USDT', 'DUSK/USDT:USDT', 'DOGE/USDT:USDT', 'DENT/USDT:USDT', 'DASH/USDT:USDT', 'DOT/USDT:USDT', 'DYDX/USDT:USDT', 'ENJ/USDT:USDT', 'ETH/USDT:USDT', 'ETC/USDT:USDT', 'ENS/USDT:USDT', 'EGLD/USDT:USDT', 'FIL/USDT:USDT', 'FLM/USDT:USDT', 'GRT/USDT:USDT', 'GALA/USDT:USDT', 'HBAR/USDT:USDT', 'HOT/USDT:USDT', 'IOTX/USDT:USDT', 'ICX/USDT:USDT', 'ICP/USDT:USDT', 'IOTA/USDT:USDT', 'IOST/USDT:USDT', 'KAVA/USDT:USDT', 'KNC/USDT:USDT', 'KSM/USDT:USDT', 'LRC/USDT:USDT', 'LTC/USDT:USDT', 'LINK/USDT:USDT', 'NEAR/USDT:USDT', 'MANA/USDT:USDT', 'MTL/USDT:USDT', 'NEO/USDT:USDT', 'ONT/USDT:USDT', 'OGN/USDT:USDT', 'ONE/USDT:USDT', 'PEOPLE/USDT:USDT', 'RLC/USDT:USDT', 'RUNE/USDT:USDT', 'RVN/USDT:USDT', 'RSR/USDT:USDT', 'ROSE/USDT:USDT', 'SNX/USDT:USDT', 'SAND/USDT:USDT', 'SOL/USDT:USDT', 'SUSHI/USDT:USDT', 'SKL/USDT:USDT', 'SXP/USDT:USDT', 'STORJ/USDT:USDT', 'TRX/USDT:USDT', 'TRB/USDT:USDT', 'TLM/USDT:USDT', 'THETA/USDT:USDT', 'UNI/USDT:USDT', 'VET/USDT:USDT', 'YFI/USDT:USDT', 'ZIL/USDT:USDT', 'ZEN/USDT:USDT', 'ZRX/USDT:USDT', 'ZEC/USDT:USDT', 'XRP/USDT:USDT', 'XLM/USDT:USDT', 'XTZ/USDT:USDT', 'XMR/USDT:USDT', 'QTUM/USDT:USDT', '1INCH/USDT:USDT']
2025-05-25 15:11:59,457 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.07s
2025-05-25 15:11:59,459 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-05-25 15:11:59,459 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-05-25 15:11:59,460 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-05-25 15:11:59,460 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-05-25 15:11:59,461 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-05-25 15:11:59,462 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-05-25 15:11:59,463 - freqtrade.util.migrations.binance_mig - WARNING - Migrating binance futures pairs in database.
2025-05-25 15:11:59,469 - freqtrade.util.migrations.binance_mig - WARNING - Done migrating binance futures pairs in database.
2025-05-25 15:11:59,487 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': 'Dry run is enabled. All trades are simulated.'}
2025-05-25 15:11:59,488 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': '*Exchange:* `binance`\n*Stake per trade:* `100 USDT`\n*Minimum ROI:* `{}`\n*Stoploss:* `-0.5`\n*Position adjustment:* `Off`\n*Timeframe:* `15m`\n*Strategy:* `Pivot7`'}
2025-05-25 15:11:59,489 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDT pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-05-25 15:11:59,839 - telegram.ext.Application - INFO - Application started
2025-05-25 15:12:02,111 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(b5d139bb, ('172.18.0.1', 49776))
2025-05-25 15:12:02,134 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(1651361d, ('172.18.0.1', 49792))
2025-05-25 15:12:03,165 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(a925492b, ('172.18.0.1', 49806))
2025-05-25 15:12:32,456 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(a925492b, ('172.18.0.1', 49806))
2025-05-25 15:14:51,874 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:14:52,923 - freqtrade.freqtradebot - INFO - Long signal found: about create a new trade for AAVE/USDT:USDT with stake_amount: 100 and price: 262.21 ...
2025-05-25 15:14:53,217 - freqtrade.freqtradebot - INFO - Order dry_run_buy_AAVE/USDT:USDT_1748186092.925074 was created for AAVE/USDT:USDT and status is open.
2025-05-25 15:14:57,554 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:14:57,554 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 1, 'type': entry, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'AAVE/USDT:USDT', 'leverage': 20.0, 'direction': 'Long', 'limit': 262.21, 'open_rate': 262.21, 'order_type': 'limit', 'stake_amount': 100.0, 'stake_currency': 'USDT', 'base_currency': 'AAVE', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 7.6, 'open_date': datetime.datetime(2025, 5, 25, 15, 14, 53, 219333, tzinfo=datetime.timezone.utc), 'current_rate': 262.21, 'sub_trade': False}
2025-05-25 15:14:57,570 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:14:57,867 - freqtrade.freqtradebot - INFO - Long signal found: about create a new trade for BTC/USDT:USDT with stake_amount: 100 and price: 107235.3 ...
2025-05-25 15:14:58,161 - freqtrade.freqtradebot - INFO - Order dry_run_buy_BTC/USDT:USDT_1748186097.868302 was created for BTC/USDT:USDT and status is open.
2025-05-25 15:15:00,689 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:15:00,690 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 2, 'type': entry, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'BTC/USDT:USDT', 'leverage': 20.0, 'direction': 'Long', 'limit': 107235.3, 'open_rate': 107235.3, 'order_type': 'limit', 'stake_amount': 100.0, 'stake_currency': 'USDT', 'base_currency': 'BTC', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 0.018, 'open_date': datetime.datetime(2025, 5, 25, 15, 14, 58, 162470, tzinfo=datetime.timezone.utc), 'current_rate': 107235.3, 'sub_trade': False}
2025-05-25 15:15:00,753 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:15:01,049 - freqtrade.freqtradebot - INFO - Long signal found: about create a new trade for ZEC/USDT:USDT with stake_amount: 100 and price: 48.84 ...
2025-05-25 15:15:01,341 - freqtrade.freqtradebot - INFO - Order dry_run_buy_ZEC/USDT:USDT_1748186101.050569 was created for ZEC/USDT:USDT and status is open.
2025-05-25 15:15:03,866 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:15:03,867 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 3, 'type': entry, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'ZEC/USDT:USDT', 'leverage': 10.0, 'direction': 'Long', 'limit': 48.84, 'open_rate': 48.84, 'order_type': 'limit', 'stake_amount': 100.0, 'stake_currency': 'USDT', 'base_currency': 'ZEC', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 20.475, 'open_date': datetime.datetime(2025, 5, 25, 15, 15, 1, 341839, tzinfo=datetime.timezone.utc), 'current_rate': 48.84, 'sub_trade': False}
2025-05-25 15:15:03,879 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:15:38,492 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(1651361d, ('172.18.0.1', 49792))
2025-05-25 15:15:38,494 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(b5d139bb, ('172.18.0.1', 49776))
2025-05-25 15:15:39,040 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(6cafa382, ('172.18.0.1', 57452))
2025-05-25 15:15:39,068 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(28629343, ('172.18.0.1', 57458))
2025-05-25 15:17:11,196 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(28629343, ('172.18.0.1', 57458))
2025-05-25 15:17:11,198 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(6cafa382, ('172.18.0.1', 57452))
2025-05-25 15:17:11,923 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(0c98a64f, ('172.18.0.1', 50718))
2025-05-25 15:17:11,955 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(38875f7b, ('172.18.0.1', 50724))
2025-05-25 15:17:12,057 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(3817d2ea, ('172.18.0.1', 50740))
2025-05-25 15:17:58,114 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=AAVE/USDT:USDT, amount=0.00000000, is_short=False, leverage=20.0, open_rate=262.21000000, open_since=2025-05-25 15:14:53)
2025-05-25 15:17:58,129 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=1, pair=AAVE/USDT:USDT, amount=0.00000000, is_short=False, leverage=20.0, open_rate=262.21000000, open_since=2025-05-25 15:14:53) [buy]: 0.3985592 USDT - rate: 0.0002
2025-05-25 15:17:58,130 - freqtrade.persistence.trade_model - INFO - Updating trade (id=1) ...
2025-05-25 15:17:58,131 - freqtrade.persistence.trade_model - INFO - LIMIT_BUY has been fulfilled for Trade(id=1, pair=AAVE/USDT:USDT, amount=7.60000000, is_short=False, leverage=20.0, open_rate=262.21000000, open_since=2025-05-25 15:14:53).
2025-05-25 15:17:58,138 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:17:58,143 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 1, 'type': entry_fill, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'AAVE/USDT:USDT', 'leverage': 20.0, 'direction': 'Long', 'limit': 262.21, 'open_rate': 262.21, 'order_type': 'limit', 'stake_amount': 99.63980000000001, 'stake_currency': 'USDT', 'base_currency': 'AAVE', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 7.6, 'open_date': datetime.datetime(2025, 5, 25, 15, 14, 53, 219333, tzinfo=datetime.timezone.utc), 'current_rate': 262.21, 'sub_trade': False}
2025-05-25 15:17:58,439 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=BTC/USDT:USDT, amount=0.00000000, is_short=False, leverage=20.0, open_rate=107235.30000000, open_since=2025-05-25 15:14:58)
2025-05-25 15:17:58,687 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=2, pair=BTC/USDT:USDT, amount=0.00000000, is_short=False, leverage=20.0, open_rate=107235.30000000, open_since=2025-05-25 15:14:58) [buy]: 0.38604708 USDT - rate: 0.0002
2025-05-25 15:17:58,688 - freqtrade.persistence.trade_model - INFO - Updating trade (id=2) ...
2025-05-25 15:17:58,689 - freqtrade.persistence.trade_model - INFO - LIMIT_BUY has been fulfilled for Trade(id=2, pair=BTC/USDT:USDT, amount=0.01800000, is_short=False, leverage=20.0, open_rate=107235.30000000, open_since=2025-05-25 15:14:58).
2025-05-25 15:17:58,700 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:17:58,706 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 2, 'type': entry_fill, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'BTC/USDT:USDT', 'leverage': 20.0, 'direction': 'Long', 'limit': 107235.3, 'open_rate': 107235.3, 'order_type': 'limit', 'stake_amount': 96.51177, 'stake_currency': 'USDT', 'base_currency': 'BTC', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 0.018, 'open_date': datetime.datetime(2025, 5, 25, 15, 14, 58, 162470, tzinfo=datetime.timezone.utc), 'current_rate': 107235.3, 'sub_trade': False}
2025-05-25 15:17:59,002 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:17:59,624 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:17:59,920 - freqtrade.freqtradebot - INFO - Short signal found: about create a new trade for CHR/USDT:USDT with stake_amount: 100 and price: 0.0935 ...
2025-05-25 15:18:00,214 - freqtrade.freqtradebot - INFO - Order dry_run_sell_CHR/USDT:USDT_1748186279.921354 was created for CHR/USDT:USDT and status is open.
2025-05-25 15:18:04,536 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:18:04,537 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 4, 'type': entry, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'CHR/USDT:USDT', 'leverage': 20.0, 'direction': 'Short', 'limit': 0.0935, 'open_rate': 0.0935, 'order_type': 'limit', 'stake_amount': 100.0, 'stake_currency': 'USDT', 'base_currency': 'CHR', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 21390.0, 'open_date': datetime.datetime(2025, 5, 25, 15, 18, 0, 215449, tzinfo=datetime.timezone.utc), 'current_rate': 0.0935, 'sub_trade': False}
2025-05-25 15:18:04,608 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:18:05,035 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:18:05,338 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=4, pair=CHR/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.09350000, open_since=2025-05-25 15:18:00)
2025-05-25 15:18:05,349 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=4, pair=CHR/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.09350000, open_since=2025-05-25 15:18:00) [sell]: 0.399993 USDT - rate: 0.0002
2025-05-25 15:18:05,350 - freqtrade.persistence.trade_model - INFO - Updating trade (id=4) ...
2025-05-25 15:18:05,350 - freqtrade.persistence.trade_model - INFO - LIMIT_SELL has been fulfilled for Trade(id=4, pair=CHR/USDT:USDT, amount=21390.00000000, is_short=True, leverage=20.0, open_rate=0.09350000, open_since=2025-05-25 15:18:00).
2025-05-25 15:18:05,357 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:18:05,369 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 4, 'type': entry_fill, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'CHR/USDT:USDT', 'leverage': 20.0, 'direction': 'Short', 'limit': 0.0935, 'open_rate': 0.0935, 'order_type': 'limit', 'stake_amount': 99.99825, 'stake_currency': 'USDT', 'base_currency': 'CHR', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 21390.0, 'open_date': datetime.datetime(2025, 5, 25, 15, 18, 0, 215449, tzinfo=datetime.timezone.utc), 'current_rate': 0.0935, 'sub_trade': False}
2025-05-25 15:18:10,025 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:18:15,032 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:18:20,023 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:18:25,030 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:18:30,024 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:18:35,023 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:18:40,027 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:18:45,026 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:18:50,041 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:18:55,033 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:19:00,028 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:19:04,613 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:19:05,029 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:19:10,041 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:19:15,030 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:19:20,028 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:19:25,038 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:19:30,032 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:19:35,041 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:19:40,030 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:19:45,031 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:19:50,038 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:19:55,033 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:20:00,031 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:20:04,619 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:20:05,037 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:20:10,040 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:20:15,044 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:20:20,038 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:20:25,034 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:20:30,036 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:20:35,034 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:20:40,041 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:20:45,047 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:20:50,036 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:20:55,036 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:21:00,392 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:21:04,626 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:21:05,042 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:21:10,049 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:21:15,049 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:21:20,438 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:21:25,041 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:21:30,054 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:21:35,039 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:21:40,044 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01)
2025-05-25 15:21:40,056 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01) [buy]: 0.1999998 USDT - rate: 0.0002
2025-05-25 15:21:40,057 - freqtrade.persistence.trade_model - INFO - Updating trade (id=3) ...
2025-05-25 15:21:40,058 - freqtrade.persistence.trade_model - INFO - LIMIT_BUY has been fulfilled for Trade(id=3, pair=ZEC/USDT:USDT, amount=20.47500000, is_short=False, leverage=10.0, open_rate=48.84000000, open_since=2025-05-25 15:15:01).
2025-05-25 15:21:40,071 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:21:40,442 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 3, 'type': entry_fill, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'ZEC/USDT:USDT', 'leverage': 10.0, 'direction': 'Long', 'limit': 48.84, 'open_rate': 48.84, 'order_type': 'limit', 'stake_amount': 99.9999, 'stake_currency': 'USDT', 'base_currency': 'ZEC', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 20.475, 'open_date': datetime.datetime(2025, 5, 25, 15, 15, 1, 341839, tzinfo=datetime.timezone.utc), 'current_rate': 48.83, 'sub_trade': False}
2025-05-25 15:21:56,060 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:21:56,357 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit, 'trade_id': 1, 'exchange': 'Binance', 'pair': 'AAVE/USDT:USDT', 'leverage': 20.0, 'direction': 'Long', 'gain': 'loss', 'limit': 260.29, 'order_rate': 260.29, 'order_type': 'limit', 'amount': 7.6, 'open_rate': 262.21, 'close_rate': 260.29, 'current_rate': 260.29, 'profit_amount': -15.3862, 'profit_ratio': -0.15438734, 'buy_tag': '', 'enter_tag': '', 'exit_reason': 'force_exit', 'open_date': datetime.datetime(2025, 5, 25, 15, 14, 53, 219333, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 5, 25, 15, 21, 56, 357856, tzinfo=datetime.timezone.utc), 'stake_amount': 99.63980000000001, 'stake_currency': 'USDT', 'base_currency': 'AAVE', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'sub_trade': False, 'cumulative_profit': 0.0, 'final_profit_ratio': None, 'is_final_exit': False}
2025-05-25 15:21:59,197 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:21:59,493 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit, 'trade_id': 2, 'exchange': 'Binance', 'pair': 'BTC/USDT:USDT', 'leverage': 20.0, 'direction': 'Long', 'gain': 'loss', 'limit': 106950.2, 'order_rate': 106950.2, 'order_type': 'limit', 'amount': 0.018, 'open_rate': 107235.3, 'close_rate': 106950.2, 'current_rate': 106950.2, 'profit_amount': -5.9028678, 'profit_ratio': -0.06114992, 'buy_tag': '', 'enter_tag': '', 'exit_reason': 'force_exit', 'open_date': datetime.datetime(2025, 5, 25, 15, 14, 58, 162470, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 5, 25, 15, 21, 59, 493652, tzinfo=datetime.timezone.utc), 'stake_amount': 96.51177, 'stake_currency': 'USDT', 'base_currency': 'BTC', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'sub_trade': False, 'cumulative_profit': 0.0, 'final_profit_ratio': None, 'is_final_exit': False}
2025-05-25 15:22:02,328 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:22:02,624 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit, 'trade_id': 3, 'exchange': 'Binance', 'pair': 'ZEC/USDT:USDT', 'leverage': 10.0, 'direction': 'Long', 'gain': 'profit', 'limit': 48.86, 'order_rate': 48.86, 'order_type': 'limit', 'amount': 20.475, 'open_rate': 48.84, 'close_rate': 48.86, 'current_rate': 48.86, 'profit_amount': 0.0094185, 'profit_ratio': 9.417e-05, 'buy_tag': '', 'enter_tag': '', 'exit_reason': 'force_exit', 'open_date': datetime.datetime(2025, 5, 25, 15, 15, 1, 341839, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 5, 25, 15, 22, 2, 624686, tzinfo=datetime.timezone.utc), 'stake_amount': 99.9999, 'stake_currency': 'USDT', 'base_currency': 'ZEC', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'sub_trade': False, 'cumulative_profit': 0.0, 'final_profit_ratio': None, 'is_final_exit': False}
2025-05-25 15:22:05,464 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:22:05,762 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': exit, 'trade_id': 4, 'exchange': 'Binance', 'pair': 'CHR/USDT:USDT', 'leverage': 20.0, 'direction': 'Short', 'gain': 'profit', 'limit': 0.0929, 'order_rate': 0.0929, 'order_type': 'limit', 'amount': 21390.0, 'open_rate': 0.0935, 'close_rate': 0.0929, 'current_rate': 0.0929, 'profit_amount': 12.0365808, 'profit_ratio': 0.12039199, 'buy_tag': '', 'enter_tag': '', 'exit_reason': 'force_exit', 'open_date': datetime.datetime(2025, 5, 25, 15, 18, 0, 215449, tzinfo=datetime.timezone.utc), 'close_date': datetime.datetime(2025, 5, 25, 15, 22, 5, 762114, tzinfo=datetime.timezone.utc), 'stake_amount': 99.99825, 'stake_currency': 'USDT', 'base_currency': 'CHR', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'sub_trade': False, 'cumulative_profit': 0.0, 'final_profit_ratio': None, 'is_final_exit': False}
2025-05-25 15:22:05,777 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:22:07,034 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:22:07,035 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'stopped'}
2025-05-25 15:22:07,036 - freqtrade.worker - INFO - Changing state from RUNNING to: STOPPED
2025-05-25 15:22:07,039 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': "4 open trades active.\n\nHandle these trades manually on Binance, or '/start' the bot again and use '/stopentry' to handle open trades gracefully. \nNote: Trades are simulated (dry run)."}
2025-05-25 15:22:12,040 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='STOPPED'
2025-05-25 15:23:12,043 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='STOPPED'
2025-05-25 15:24:12,046 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='STOPPED'
2025-05-25 15:25:17,046 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='STOPPED'
2025-05-25 15:26:22,045 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='STOPPED'
2025-05-25 15:27:02,742 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-05-25 15:27:02,743 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-05-25 15:27:02,744 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-05-25 15:27:02,748 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': "4 open trades active.\n\nHandle these trades manually on Binance, or '/start' the bot again and use '/stopentry' to handle open trades gracefully. \nNote: Trades are simulated (dry run)."}
2025-05-25 15:27:02,749 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-05-25 15:27:02,750 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-05-25 15:27:02,751 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-05-25 15:27:02,792 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(3817d2ea, ('172.18.0.1', 50740))
2025-05-25 15:27:02,793 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(38875f7b, ('172.18.0.1', 50724))
2025-05-25 15:27:02,794 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(0c98a64f, ('172.18.0.1', 50718))
2025-05-25 15:27:02,892 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.telegram ...
2025-05-25 15:27:03,286 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-05-25 15:27:03,287 - telegram.ext.Application - INFO - Application.stop() complete
2025-05-25 15:27:10,865 - freqtrade - INFO - SIGINT received, aborting ...
2025-05-25 15:32:44,799 - freqtrade.loggers - INFO - Enabling colorized output.
2025-05-25 15:32:44,799 - root - INFO - Logfile configured
2025-05-25 15:32:44,800 - freqtrade.loggers - INFO - Verbosity set to 0
2025-05-25 15:32:44,801 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-05-25 15:32:44,801 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-05-25 15:32:44,802 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:///tradesv3.dryrun.sqlite"
2025-05-25 15:32:44,802 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 30 ...
2025-05-25 15:32:47,589 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-05-25 15:32:47,590 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-05-25 15:32:47,591 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-05-25 15:32:47,603 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-05-25 15:32:47,604 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-05-25 15:32:47,689 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/AdvancedFuturesAIPower.py due to 'No module named 'freqtrade.freqai.FreqaiStrategy''
2025-05-25 15:32:47,863 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/GodStra.py due to 'No module named 'ta''
2025-05-25 15:32:47,868 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/Heracles.py due to 'No module named 'ta''
2025-05-25 15:32:48,066 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy Pivot7 from '/freqtrade/user_data/strategies/Pivot7.py'...
2025-05-25 15:32:48,067 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-05-25 15:32:48,068 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 15m.
2025-05-25 15:32:48,069 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-05-25 15:32:48,069 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: 100.
2025-05-25 15:32:48,070 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-05-25 15:32:48,070 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 30.
2025-05-25 15:32:48,071 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {}
2025-05-25 15:32:48,071 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 15m
2025-05-25 15:32:48,072 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.5
2025-05-25 15:32:48,072 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-05-25 15:32:48,073 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.1
2025-05-25 15:32:48,073 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.5
2025-05-25 15:32:48,074 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-05-25 15:32:48,074 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-05-25 15:32:48,075 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-05-25 15:32:48,075 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 'stoploss_on_exchange_interval': 60}
2025-05-25 15:32:48,075 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-05-25 15:32:48,076 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-05-25 15:32:48,076 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: 100
2025-05-25 15:32:48,077 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 0
2025-05-25 15:32:48,077 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-05-25 15:32:48,078 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-05-25 15:32:48,078 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-05-25 15:32:48,079 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-05-25 15:32:48,079 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-05-25 15:32:48,079 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-05-25 15:32:48,080 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-05-25 15:32:48,080 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-05-25 15:32:48,081 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-05-25 15:32:48,081 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 30
2025-05-25 15:32:48,082 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-05-25 15:32:48,085 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-05-25 15:32:48,085 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.69
2025-05-25 15:32:48,086 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}}
2025-05-25 15:32:48,103 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'swap'}, 'enableRateLimit': True, 'rateLimit': 200}
2025-05-25 15:32:48,119 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-05-25 15:32:51,212 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2025-05-25 15:32:51,482 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:32:51,949 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-05-25 15:32:52,106 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-05-25 15:32:52,378 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-05-25 15:32:52,379 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-05-25 15:32:52,411 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-05-25 15:32:52,412 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-05-25 15:32:52,469 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['edge'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info']]
2025-05-25 15:32:52,475 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair AUDIO/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:32:52,477 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair BTS/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:32:52,477 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring BLZ/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:32:52,478 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair EOS/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:32:52,479 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring FTM/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:32:52,480 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring KLAY/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:32:52,483 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair LUNA/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:32:52,483 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring LINA/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:32:52,484 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair MATIC/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:32:52,484 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring OMG/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:32:52,485 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring OCEAN/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:32:52,486 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring REEF/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:32:52,486 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair SRM/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:32:52,487 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair TOMO/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-05-25 15:32:52,488 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring UNFI/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:32:52,488 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring WAVES/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:32:52,489 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring XEM/USDT:USDT from whitelist. Market is not active.
2025-05-25 15:32:52,493 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 86 pairs: ['AAVE/USDT:USDT', 'ALICE/USDT:USDT', 'ARPA/USDT:USDT', 'AVAX/USDT:USDT', 'ATOM/USDT:USDT', 'ANKR/USDT:USDT', 'AXS/USDT:USDT', 'ADA/USDT:USDT', 'ALGO/USDT:USDT', 'BAND/USDT:USDT', 'BEL/USDT:USDT', 'BNB/USDT:USDT', 'BTC/USDT:USDT', 'BAT/USDT:USDT', 'CHR/USDT:USDT', 'C98/USDT:USDT', 'COTI/USDT:USDT', 'CHZ/USDT:USDT', 'COMP/USDT:USDT', 'CRV/USDT:USDT', 'CELO/USDT:USDT', 'DUSK/USDT:USDT', 'DOGE/USDT:USDT', 'DENT/USDT:USDT', 'DASH/USDT:USDT', 'DOT/USDT:USDT', 'DYDX/USDT:USDT', 'ENJ/USDT:USDT', 'ETH/USDT:USDT', 'ETC/USDT:USDT', 'ENS/USDT:USDT', 'EGLD/USDT:USDT', 'FIL/USDT:USDT', 'FLM/USDT:USDT', 'GRT/USDT:USDT', 'GALA/USDT:USDT', 'HBAR/USDT:USDT', 'HOT/USDT:USDT', 'IOTX/USDT:USDT', 'ICX/USDT:USDT', 'ICP/USDT:USDT', 'IOTA/USDT:USDT', 'IOST/USDT:USDT', 'KAVA/USDT:USDT', 'KNC/USDT:USDT', 'KSM/USDT:USDT', 'LRC/USDT:USDT', 'LTC/USDT:USDT', 'LINK/USDT:USDT', 'NEAR/USDT:USDT', 'MANA/USDT:USDT', 'MTL/USDT:USDT', 'NEO/USDT:USDT', 'ONT/USDT:USDT', 'OGN/USDT:USDT', 'ONE/USDT:USDT', 'PEOPLE/USDT:USDT', 'RLC/USDT:USDT', 'RUNE/USDT:USDT', 'RVN/USDT:USDT', 'RSR/USDT:USDT', 'ROSE/USDT:USDT', 'SNX/USDT:USDT', 'SAND/USDT:USDT', 'SOL/USDT:USDT', 'SUSHI/USDT:USDT', 'SKL/USDT:USDT', 'SXP/USDT:USDT', 'STORJ/USDT:USDT', 'TRX/USDT:USDT', 'TRB/USDT:USDT', 'TLM/USDT:USDT', 'THETA/USDT:USDT', 'UNI/USDT:USDT', 'VET/USDT:USDT', 'YFI/USDT:USDT', 'ZIL/USDT:USDT', 'ZEN/USDT:USDT', 'ZRX/USDT:USDT', 'ZEC/USDT:USDT', 'XRP/USDT:USDT', 'XLM/USDT:USDT', 'XTZ/USDT:USDT', 'XMR/USDT:USDT', 'QTUM/USDT:USDT', '1INCH/USDT:USDT']
2025-05-25 15:32:52,494 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.08s
2025-05-25 15:32:52,496 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-05-25 15:32:52,496 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-05-25 15:32:52,497 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-05-25 15:32:52,497 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-05-25 15:32:52,498 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-05-25 15:32:52,499 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-05-25 15:32:52,500 - freqtrade.util.migrations.binance_mig - WARNING - Migrating binance futures pairs in database.
2025-05-25 15:32:52,505 - freqtrade.util.migrations.binance_mig - WARNING - Done migrating binance futures pairs in database.
2025-05-25 15:32:52,528 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': 'Dry run is enabled. All trades are simulated.'}
2025-05-25 15:32:52,529 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': '*Exchange:* `binance`\n*Stake per trade:* `100 USDT`\n*Minimum ROI:* `{}`\n*Stoploss:* `-0.5`\n*Position adjustment:* `Off`\n*Timeframe:* `15m`\n*Strategy:* `Pivot7`'}
2025-05-25 15:32:52,531 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDT pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-05-25 15:32:52,915 - telegram.ext.Application - INFO - Application started
2025-05-25 15:33:10,060 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(62a3f61c, ('172.18.0.1', 45098))
2025-05-25 15:33:10,083 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(379a6e5f, ('172.18.0.1', 45108))
2025-05-25 15:33:12,169 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(fffab629, ('172.18.0.1', 45122))
2025-05-25 15:33:53,672 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(fffab629, ('172.18.0.1', 45122))
2025-05-25 15:35:44,965 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:35:45,996 - freqtrade.freqtradebot - INFO - Long signal found: about create a new trade for FLM/USDT:USDT with stake_amount: 100 and price: 0.0429 ...
2025-05-25 15:35:46,293 - freqtrade.freqtradebot - INFO - Order dry_run_buy_FLM/USDT:USDT_1748187345.998179 was created for FLM/USDT:USDT and status is open.
2025-05-25 15:35:49,414 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:35:49,414 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 1, 'type': entry, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'FLM/USDT:USDT', 'leverage': 20.0, 'direction': 'Long', 'limit': 0.0429, 'open_rate': 0.0429, 'order_type': 'limit', 'stake_amount': 100.0, 'stake_currency': 'USDT', 'base_currency': 'FLM', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 46620.0, 'open_date': datetime.datetime(2025, 5, 25, 15, 35, 46, 295253, tzinfo=datetime.timezone.utc), 'current_rate': 0.0429, 'sub_trade': False}
2025-05-25 15:35:49,450 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:35:49,744 - freqtrade.freqtradebot - INFO - Short signal found: about create a new trade for TRX/USDT:USDT with stake_amount: 100 and price: 0.27032 ...
2025-05-25 15:35:50,040 - freqtrade.freqtradebot - INFO - Order dry_run_sell_TRX/USDT:USDT_1748187349.745679 was created for TRX/USDT:USDT and status is open.
2025-05-25 15:35:52,556 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:35:52,556 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 2, 'type': entry, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'TRX/USDT:USDT', 'leverage': 20.0, 'direction': 'Short', 'limit': 0.27032, 'open_rate': 0.27032, 'order_type': 'limit', 'stake_amount': 100.0, 'stake_currency': 'USDT', 'base_currency': 'TRX', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 7398.0, 'open_date': datetime.datetime(2025, 5, 25, 15, 35, 50, 41853, tzinfo=datetime.timezone.utc), 'current_rate': 0.27032, 'sub_trade': False}
2025-05-25 15:35:52,572 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:35:52,870 - freqtrade.freqtradebot - INFO - Long signal found: about create a new trade for ZEC/USDT:USDT with stake_amount: 100 and price: 49.51 ...
2025-05-25 15:35:53,163 - freqtrade.freqtradebot - INFO - Order dry_run_buy_ZEC/USDT:USDT_1748187352.871581 was created for ZEC/USDT:USDT and status is open.
2025-05-25 15:35:55,684 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:35:55,684 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 3, 'type': entry, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'ZEC/USDT:USDT', 'leverage': 10.0, 'direction': 'Long', 'limit': 49.51, 'open_rate': 49.51, 'order_type': 'limit', 'stake_amount': 100.0, 'stake_currency': 'USDT', 'base_currency': 'ZEC', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 20.197, 'open_date': datetime.datetime(2025, 5, 25, 15, 35, 53, 164685, tzinfo=datetime.timezone.utc), 'current_rate': 49.51, 'sub_trade': False}
2025-05-25 15:35:55,693 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:35:56,135 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=FLM/USDT:USDT, amount=0.00000000, is_short=False, leverage=20.0, open_rate=0.04290000, open_since=2025-05-25 15:35:46)
2025-05-25 15:35:56,443 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:35:56,748 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=49.51000000, open_since=2025-05-25 15:35:53)
2025-05-25 15:36:01,123 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=FLM/USDT:USDT, amount=0.00000000, is_short=False, leverage=20.0, open_rate=0.04290000, open_since=2025-05-25 15:35:46)
2025-05-25 15:36:01,708 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:36:02,018 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=49.51000000, open_since=2025-05-25 15:35:53)
2025-05-25 15:36:06,120 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=FLM/USDT:USDT, amount=0.00000000, is_short=False, leverage=20.0, open_rate=0.04290000, open_since=2025-05-25 15:35:46)
2025-05-25 15:36:06,429 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:36:06,733 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=49.51000000, open_since=2025-05-25 15:35:53)
2025-05-25 15:36:11,118 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=FLM/USDT:USDT, amount=0.00000000, is_short=False, leverage=20.0, open_rate=0.04290000, open_since=2025-05-25 15:35:46)
2025-05-25 15:36:11,423 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:36:11,729 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=49.51000000, open_since=2025-05-25 15:35:53)
2025-05-25 15:36:11,742 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=3, pair=ZEC/USDT:USDT, amount=0.00000000, is_short=False, leverage=10.0, open_rate=49.51000000, open_since=2025-05-25 15:35:53) [buy]: 0.19999069 USDT - rate: 0.0002
2025-05-25 15:36:11,743 - freqtrade.persistence.trade_model - INFO - Updating trade (id=3) ...
2025-05-25 15:36:11,744 - freqtrade.persistence.trade_model - INFO - LIMIT_BUY has been fulfilled for Trade(id=3, pair=ZEC/USDT:USDT, amount=20.19700000, is_short=False, leverage=10.0, open_rate=49.51000000, open_since=2025-05-25 15:35:53).
2025-05-25 15:36:11,751 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:36:11,756 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 3, 'type': entry_fill, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'ZEC/USDT:USDT', 'leverage': 10.0, 'direction': 'Long', 'limit': 49.51, 'open_rate': 49.51, 'order_type': 'limit', 'stake_amount': 99.99534700000001, 'stake_currency': 'USDT', 'base_currency': 'ZEC', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 20.197, 'open_date': datetime.datetime(2025, 5, 25, 15, 35, 53, 164685, tzinfo=datetime.timezone.utc), 'current_rate': 49.51, 'sub_trade': False}
2025-05-25 15:36:16,144 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=FLM/USDT:USDT, amount=0.00000000, is_short=False, leverage=20.0, open_rate=0.04290000, open_since=2025-05-25 15:35:46)
2025-05-25 15:36:16,459 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:36:21,118 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=FLM/USDT:USDT, amount=0.00000000, is_short=False, leverage=20.0, open_rate=0.04290000, open_since=2025-05-25 15:35:46)
2025-05-25 15:36:21,426 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:36:26,123 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=FLM/USDT:USDT, amount=0.00000000, is_short=False, leverage=20.0, open_rate=0.04290000, open_since=2025-05-25 15:35:46)
2025-05-25 15:36:26,435 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:36:31,116 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=FLM/USDT:USDT, amount=0.00000000, is_short=False, leverage=20.0, open_rate=0.04290000, open_since=2025-05-25 15:35:46)
2025-05-25 15:36:31,432 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:36:36,113 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=1, pair=FLM/USDT:USDT, amount=0.00000000, is_short=False, leverage=20.0, open_rate=0.04290000, open_since=2025-05-25 15:35:46)
2025-05-25 15:36:36,123 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=1, pair=FLM/USDT:USDT, amount=0.00000000, is_short=False, leverage=20.0, open_rate=0.04290000, open_since=2025-05-25 15:35:46) [buy]: 0.3999996 USDT - rate: 0.0002
2025-05-25 15:36:36,124 - freqtrade.persistence.trade_model - INFO - Updating trade (id=1) ...
2025-05-25 15:36:36,125 - freqtrade.persistence.trade_model - INFO - LIMIT_BUY has been fulfilled for Trade(id=1, pair=FLM/USDT:USDT, amount=46620.00000000, is_short=False, leverage=20.0, open_rate=0.04290000, open_since=2025-05-25 15:35:46).
2025-05-25 15:36:36,139 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:36:36,152 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 1, 'type': entry_fill, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'FLM/USDT:USDT', 'leverage': 20.0, 'direction': 'Long', 'limit': 0.0429, 'open_rate': 0.0429, 'order_type': 'limit', 'stake_amount': 99.9999, 'stake_currency': 'USDT', 'base_currency': 'FLM', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 46620.0, 'open_date': datetime.datetime(2025, 5, 25, 15, 35, 46, 295253, tzinfo=datetime.timezone.utc), 'current_rate': 0.0429, 'sub_trade': False}
2025-05-25 15:36:36,448 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:36:41,113 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:36:46,114 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:36:51,117 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:36:56,118 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:37:00,693 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:37:01,119 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:37:06,117 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:37:11,116 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:37:16,114 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:37:21,119 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:37:26,116 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:37:31,123 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:37:36,110 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:37:41,114 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:37:46,116 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:37:51,118 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:37:56,135 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:38:01,112 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:38:05,690 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:38:06,910 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:38:11,115 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:38:16,123 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:38:21,115 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:38:26,117 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:38:31,160 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:38:36,112 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:38:41,112 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:38:46,114 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:38:51,116 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:38:56,119 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:39:01,113 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:39:06,115 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:39:10,690 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:39:11,113 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:39:16,124 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:39:21,110 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:39:26,121 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:39:31,113 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:39:36,127 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:39:41,114 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:39:46,118 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:39:51,138 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:39:56,122 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:40:01,132 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:40:06,118 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:40:10,691 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:40:11,143 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:40:16,130 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:40:21,116 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:40:26,113 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:40:31,117 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:40:36,114 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:40:41,119 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:40:46,112 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:40:51,117 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:40:56,116 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:41:01,118 - freqtrade.freqtradebot - INFO - Found open order for Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50)
2025-05-25 15:41:01,127 - freqtrade.freqtradebot - INFO - Fee for Trade Trade(id=2, pair=TRX/USDT:USDT, amount=0.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50) [sell]: 0.39996547 USDT - rate: 0.0002
2025-05-25 15:41:01,128 - freqtrade.persistence.trade_model - INFO - Updating trade (id=2) ...
2025-05-25 15:41:01,129 - freqtrade.persistence.trade_model - INFO - LIMIT_SELL has been fulfilled for Trade(id=2, pair=TRX/USDT:USDT, amount=7398.00000000, is_short=True, leverage=20.0, open_rate=0.27032000, open_since=2025-05-25 15:35:50).
2025-05-25 15:41:01,135 - freqtrade.wallets - INFO - Wallets synced.
2025-05-25 15:41:01,432 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'trade_id': 2, 'type': entry_fill, 'buy_tag': '', 'enter_tag': '', 'exchange': 'Binance', 'pair': 'TRX/USDT:USDT', 'leverage': 20.0, 'direction': 'Short', 'limit': 0.27032, 'open_rate': 0.27032, 'order_type': 'limit', 'stake_amount': 99.991368, 'stake_currency': 'USDT', 'base_currency': 'TRX', 'quote_currency': 'USDT', 'fiat_currency': 'USD', 'amount': 7398.0, 'open_date': datetime.datetime(2025, 5, 25, 15, 35, 50, 41853, tzinfo=datetime.timezone.utc), 'current_rate': 0.27037, 'sub_trade': False}
2025-05-25 15:41:10,691 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:42:10,692 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:43:10,693 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:44:15,693 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:48:02,483 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:49:07,482 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:50:12,482 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:51:12,485 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:52:12,485 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:53:12,485 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:54:17,485 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:55:17,489 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:56:17,490 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:57:17,491 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:58:17,496 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.3', state='RUNNING'
2025-05-25 15:58:39,988 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-05-25 15:58:39,989 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-05-25 15:58:39,990 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-05-25 15:58:39,995 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': "3 open trades active.\n\nHandle these trades manually on Binance, or '/start' the bot again and use '/stopentry' to handle open trades gracefully. \nNote: Trades are simulated (dry run)."}
2025-05-25 15:58:39,996 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-05-25 15:58:39,998 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-05-25 15:58:39,998 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-05-25 15:58:40,046 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(62a3f61c, ('172.18.0.1', 45098))
2025-05-25 15:58:40,047 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(379a6e5f, ('172.18.0.1', 45108))
2025-05-25 15:58:40,146 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.telegram ...
2025-05-25 15:58:40,402 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-05-25 15:58:40,404 - telegram.ext.Application - INFO - Application.stop() complete
2025-05-25 15:58:40,406 - freqtrade.rpc.telegram - WARNING - Telegram NetworkError: httpx.ReadError: ! Trying one more time.
2025-05-25 15:58:40,407 - freqtrade.rpc.telegram - WARNING - TelegramError: Unknown error in HTTP implementation: RuntimeError('This HTTPXRequest is not initialized!')! Giving up on that message.
2025-05-25 15:58:40,408 - freqtrade.rpc.telegram - WARNING - Telegram NetworkError: httpx.ReadError: ! Trying one more time.
2025-05-25 15:58:40,409 - freqtrade.rpc.telegram - WARNING - TelegramError: Unknown error in HTTP implementation: RuntimeError('This HTTPXRequest is not initialized!')! Giving up on that message.
2025-05-25 15:58:44,416 - freqtrade - INFO - SIGINT received, aborting ...
