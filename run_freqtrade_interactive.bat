@echo off
echo Stopping any running FreqTrade containers...
docker compose down

echo Starting FreqTrade in interactive mode...
docker run --rm -it ^
  -v "%cd%/user_data:/freqtrade/user_data" ^
  -p 8080:8080 ^
  freqtradeorg/freqtrade:develop_plot ^
  webserver ^
  --config /freqtrade/user_data/backtest_config.json ^
  --strategy HighLeverageTrendFollowing ^
  --db-url sqlite:////freqtrade/user_data/tradesv3_backtest.sqlite ^
  --no-telegram ^
  --enable-openapi ^
  --enable-ui-frame

echo Press any key to exit...
pause > nul
