{"max_open_trades": 3, "stake_currency": "USDT", "stake_amount": 100, "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "cancel_open_orders_on_exit": false, "trading_mode": "futures", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {}, "ccxt_async_config": {}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT", "ADA/USDT:USDT", "XRP/USDT:USDT", "SOL/USDT:USDT", "DOT/USDT:USDT", "DOGE/USDT:USDT", "AVAX/USDT:USDT", "LINK/USDT:USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "telegram": {"enabled": false}, "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8080, "verbosity": "info", "jwt_secret_key": "somethingrandom", "CORS_origins": ["http://localhost:8081", "http://127.0.0.1:8081"], "username": "freqtrader", "password": "password"}, "bot_name": "MacheteV8b_Backtest", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}}