@echo off
echo ========================================
echo Restarting FreqTrade to load Pivot7Enhanced
echo ========================================

echo Stopping containers...
docker-compose -f docker-compose-freqUI.yml down

echo Waiting for cleanup...
timeout /t 3 /nobreak >nul

echo Starting containers...
docker-compose -f docker-compose-freqUI.yml up -d

echo Waiting for services to start...
timeout /t 10 /nobreak >nul

echo Testing Pivot7Enhanced strategy...
docker run --rm -v "%CD%/user_data:/freqtrade/user_data" freqtradeorg/freqtrade:stable list-strategies | findstr "Pivot7"

echo.
echo ========================================
echo Pivot7Enhanced should now be available!
echo ========================================
echo.
echo FreqTrade UI: http://localhost:8080
echo Username: freqtrader
echo Password: password
echo.
echo Strategy: Pivot7Enhanced
echo - Much safer than original (8%% stop vs 50%%)
echo - Dynamic leverage (3x-10x vs fixed 20x)
echo - Enhanced entry/exit logic
echo - Ready for backtesting!
echo ========================================

pause
