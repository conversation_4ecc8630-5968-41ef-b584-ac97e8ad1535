@echo off
echo Starting FreqTrade webserver...

REM Stop any running containers first
docker compose down

REM Start the webserver with API enabled
docker compose run --rm -p 8080:8080 freqtrade webserver ^
  --config user_data/config_backtest_machetev8b.json ^
  --strategy MacheteV8b ^
  --db-url sqlite:///tradesv3.sqlite ^
  --no-telegram

echo.
echo Webserver started! Access the UI at http://localhost:8080
echo Press Ctrl+C to stop the server when done.
