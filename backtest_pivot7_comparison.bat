@echo off
echo Running Pivot7 Strategy Backtests...

REM Run backtest for 10 trades, $50 stake
docker run -v "%cd%/user_data:/freqtrade/user_data" freqtrade/freqtrade:stable backtesting --config user_data/pivot7_backtest_10_50.json --strategy Pivot7Improved --timerange 20230101-20230601 --stake-amount 50 --dry-run-wallet 1000 --enable-protections

REM Run backtest for 10 trades, $100 stake
docker run -v "%cd%/user_data:/freqtrade/user_data" freqtrade/freqtrade:stable backtesting --config user_data/pivot7_backtest_10_100.json --strategy Pivot7Improved --timerange 20230101-20230601 --stake-amount 100 --dry-run-wallet 1000 --enable-protections

REM Run backtest for 30 trades, $50 stake
docker run -v "%cd%/user_data:/freqtrade/user_data" freqtrade/freqtrade:stable backtesting --config user_data/pivot7_backtest_30_50.json --strategy Pivot7Improved --timerange 20230101-20230601 --stake-amount 50 --dry-run-wallet 1000 --enable-protections

REM Run backtest for 30 trades, $100 stake
docker run -v "%cd%/user_data:/freqtrade/user_data" freqtrade/freqtrade:stable backtesting --config user_data/pivot7_backtest_30_100.json --strategy Pivot7Improved --timerange 20230101-20230601 --stake-amount 100 --dry-run-wallet 1000 --enable-protections

echo All backtests completed. Check the results in the user_data/backtest_results directory.
