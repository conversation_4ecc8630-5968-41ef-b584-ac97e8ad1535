{"max_open_trades": 30, "stake_currency": "USDT", "stake_amount": 50, "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "timeframe": "15m", "dry_run": false, "cancel_open_orders_on_exit": false, "trading_mode": "futures", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "rlhrEBqtMflMnyFbZhPbzOY4ZnEgNWWS0QdtDfxkGEjDzZ3jlOFGN3FHLvTtG3p4", "secret": "zWB5NIGnSXLtNCJaIKqnIFFrBypTh6Gi8SYJI5DIg8wTe4kqBpIG4QTxJvnSwv2x", "ccxt_config": {"enableRateLimit": true, "options": {"defaultType": "future"}}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 200, "options": {"defaultType": "future"}}, "pair_whitelist": ["AUDIO/USDT:USDT", "AAVE/USDT:USDT", "ALICE/USDT:USDT", "ARPA/USDT:USDT", "AVAX/USDT:USDT", "ATOM/USDT:USDT", "ANKR/USDT:USDT", "AXS/USDT:USDT", "ADA/USDT:USDT", "ALGO/USDT:USDT", "BTS/USDT:USDT", "BAND/USDT:USDT", "BEL/USDT:USDT", "BNB/USDT:USDT", "BTC/USDT:USDT", "BLZ/USDT:USDT", "BAT/USDT:USDT", "CHR/USDT:USDT", "C98/USDT:USDT", "COTI/USDT:USDT", "CHZ/USDT:USDT", "COMP/USDT:USDT", "CRV/USDT:USDT", "CELO/USDT:USDT", "DUSK/USDT:USDT", "DOGE/USDT:USDT", "DENT/USDT:USDT", "DASH/USDT:USDT", "DOT/USDT:USDT", "DYDX/USDT:USDT", "ENJ/USDT:USDT", "EOS/USDT:USDT", "ETH/USDT:USDT", "ETC/USDT:USDT", "ENS/USDT:USDT", "EGLD/USDT:USDT", "FIL/USDT:USDT", "FTM/USDT:USDT", "FLM/USDT:USDT", "GRT/USDT:USDT", "GALA/USDT:USDT", "HBAR/USDT:USDT", "HOT/USDT:USDT", "IOTX/USDT:USDT", "ICX/USDT:USDT", "ICP/USDT:USDT", "IOTA/USDT:USDT", "IOST/USDT:USDT", "KLAY/USDT:USDT", "KAVA/USDT:USDT", "KNC/USDT:USDT", "KSM/USDT:USDT", "LUNA/USDT:USDT", "LRC/USDT:USDT", "LINA/USDT:USDT", "LTC/USDT:USDT", "LINK/USDT:USDT", "MATIC/USDT:USDT", "NEAR/USDT:USDT", "MANA/USDT:USDT", "MTL/USDT:USDT", "NEO/USDT:USDT", "ONT/USDT:USDT", "OMG/USDT:USDT", "OCEAN/USDT:USDT", "OGN/USDT:USDT", "ONE/USDT:USDT", "PEOPLE/USDT:USDT", "RLC/USDT:USDT", "RUNE/USDT:USDT", "RVN/USDT:USDT", "RSR/USDT:USDT", "REEF/USDT:USDT", "ROSE/USDT:USDT", "SNX/USDT:USDT", "SAND/USDT:USDT", "SOL/USDT:USDT", "SUSHI/USDT:USDT", "SRM/USDT:USDT", "SKL/USDT:USDT", "SXP/USDT:USDT", "STORJ/USDT:USDT", "TRX/USDT:USDT", "TOMO/USDT:USDT", "TRB/USDT:USDT", "TLM/USDT:USDT", "THETA/USDT:USDT", "UNI/USDT:USDT", "UNFI/USDT:USDT", "VET/USDT:USDT", "YFI/USDT:USDT", "ZIL/USDT:USDT", "ZEN/USDT:USDT", "ZRX/USDT:USDT", "ZEC/USDT:USDT", "WAVES/USDT:USDT", "XRP/USDT:USDT", "XLM/USDT:USDT", "XTZ/USDT:USDT", "XMR/USDT:USDT", "XEM/USDT:USDT", "QTUM/USDT:USDT", "1INCH/USDT:USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "edge": {"enabled": false, "process_throttle_secs": 3600, "calculate_since_number_of_days": 7, "allowed_risk": 0.01, "stoploss_range_min": -0.01, "stoploss_range_max": -0.1, "stoploss_range_step": -0.01, "minimum_winrate": 0.6, "minimum_expectancy": 0.2, "min_trade_number": 10, "max_trade_duration_minute": 1440, "remove_pumps": false}, "telegram": {"enabled": true, "token": "**********************************************", "chat_id": "6077739727", "notification_settings": {"status": "on", "warning": "on", "startup": "on", "entry": "on", "exit": "on", "entry_cancel": "on", "exit_cancel": "on", "entry_fill": "on", "exit_fill": "on", "protection_trigger": "on", "protection_trigger_global": "on", "strategy_msg": "on", "show_candle": "on"}}, "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8081, "verbosity": "error", "jwt_secret_key": "something_secret", "CORS_origins": ["*"], "username": "freqtrader", "password": "trading123"}, "bot_name": "Pivot7_100USD_LIVE_TRADING", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}}