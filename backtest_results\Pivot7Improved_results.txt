2025-06-02 14:57:52,338 - freqtrade - INFO - freqtrade docker-2025.5-dev-f3d03dd2
2025-06-02 14:57:52,566 - numexpr.utils - INFO - NumExpr defaulting to 12 threads.
2025-06-02 14:57:53,615 - freqtrade.configuration.load_config - INFO - Using config: user_data/config_backtest_comparison.json ...
2025-06-02 14:57:53,619 - freqtrade.loggers - INFO - Enabling colorized output.
2025-06-02 14:57:53,620 - root - INFO - Logfile configured
2025-06-02 14:57:53,620 - freqtrade.loggers - INFO - Verbosity set to 0
2025-06-02 14:57:53,621 - freqtrade.configuration.configuration - INFO - Parameter -i/--timeframe detected ... Using timeframe: 5m ...
2025-06-02 14:57:53,621 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 20 ...
2025-06-02 14:57:53,622 - freqtrade.configuration.configuration - INFO - Parameter --dry-run-wallet detected, overriding dry_run_wallet to: 100.0 ...
2025-06-02 14:57:53,622 - freqtrade.configuration.configuration - INFO - Parameter --timerange detected: 20250101- ...
2025-06-02 14:57:55,879 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-06-02 14:57:55,880 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-06-02 14:57:55,881 - freqtrade.configuration.configuration - INFO - Overriding timeframe with Command line argument
2025-06-02 14:57:55,881 - freqtrade.configuration.configuration - INFO - Parameter --cache=day detected ...
2025-06-02 14:57:55,882 - freqtrade.configuration.configuration - INFO - Filter trades by timerange: 20250101-
2025-06-02 14:57:55,883 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-06-02 14:57:55,893 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-06-02 14:57:55,894 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-06-02 14:57:55,894 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 14:57:55,897 - freqtrade.commands.optimize_commands - INFO - Starting freqtrade in Backtesting mode
2025-06-02 14:57:55,898 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-06-02 14:57:55,898 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.82
2025-06-02 14:57:55,899 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'future'}, 'enableRateLimit': True}
2025-06-02 14:57:55,908 - freqtrade.exchange.exchange - INFO - Applying additional ccxt config: {'options': {'defaultType': 'future'}, 'enableRateLimit': True, 'rateLimit': 200}
2025-06-02 14:57:55,919 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-06-02 14:57:58,637 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2025-06-02 14:57:58,717 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/AdvancedFuturesAIPower.py due to 'No module named 
'freqtrade.freqai.FreqaiStrategy''
2025-06-02 14:57:58,880 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/GodStra.py due to 'No module named 'ta''
2025-06-02 14:57:58,886 - freqtrade.resolvers.iresolver - WARNING - Could not import /freqtrade/user_data/strategies/Heracles.py due to 'No module named 'ta''
2025-06-02 14:57:59,056 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy Pivot7Improved from '/freqtrade/user_data/strategies/pivot7_improved.py'...
2025-06-02 14:57:59,057 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-06-02 14:57:59,058 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'timeframe' with value in config file: 5m.
2025-06-02 14:57:59,058 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-06-02 14:57:59,058 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: 5.
2025-06-02 14:57:59,059 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 
'minutes'}.
2025-06-02 14:57:59,059 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 20.
2025-06-02 14:57:59,059 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'0': 0.15, '30': 0.08, '60': 0.05, '120': 0.02}
2025-06-02 14:57:59,060 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-06-02 14:57:59,060 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.08
2025-06-02 14:57:59,060 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: True
2025-06-02 14:57:59,061 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive: 0.02
2025-06-02 14:57:59,061 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.03
2025-06-02 14:57:59,061 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-06-02 14:57:59,061 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-06-02 14:57:59,062 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-06-02 14:57:59,062 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'limit', 'stoploss_on_exchange': False, 
'stoploss_on_exchange_interval': 60}
2025-06-02 14:57:59,062 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-06-02 14:57:59,063 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-06-02 14:57:59,063 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: 5
2025-06-02 14:57:59,064 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 0
2025-06-02 14:57:59,065 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-06-02 14:57:59,065 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-06-02 14:57:59,066 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-06-02 14:57:59,066 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-06-02 14:57:59,067 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-06-02 14:57:59,067 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-06-02 14:57:59,068 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-06-02 14:57:59,068 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-06-02 14:57:59,069 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-06-02 14:57:59,069 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 20
2025-06-02 14:57:59,070 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-06-02 14:57:59,089 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-06-02 14:57:59,149 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair AUDIO/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:59,150 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair BTS/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:59,151 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring BLZ/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:59,152 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair EOS/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:59,152 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring FTM/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:59,153 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring KLAY/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:59,154 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair LUNA/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:59,154 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring LINA/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:59,155 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair MATIC/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:59,155 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring OMG/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:59,156 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring OCEAN/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:59,157 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring REEF/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:59,157 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair SRM/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:59,158 - freqtrade.plugins.pairlist.IPairList - WARNING - Pair TOMO/USDT:USDT is not compatible with exchange Binance. Removing it from whitelist..
2025-06-02 14:57:59,158 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring UNFI/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:59,159 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring WAVES/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:59,159 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring XEM/USDT:USDT from whitelist. Market is not active.
2025-06-02 14:57:59,164 - freqtrade.optimize.backtesting - INFO - Using fee 0.0500% - worst case fee from exchange (lowest tier).
2025-06-02 14:57:59,191 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for AAVE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,193 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ALICE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,196 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ARPA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,290 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ANKR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,293 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for AXS/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,331 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ALGO/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,334 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for BAND/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,336 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for BEL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,418 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for BAT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,421 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for CHR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,424 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for C98/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,426 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for COTI/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,428 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for CHZ/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,430 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for COMP/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,434 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for CRV/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,436 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for CELO/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,438 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for DUSK/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,479 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for DENT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,482 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for DASH/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,525 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for DYDX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,527 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ENJ/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,579 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ETC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,582 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ENS/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,584 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for EGLD/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,586 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for FIL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,589 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for FLM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,591 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for GRT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,593 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for GALA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,595 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for HBAR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,598 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for HOT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,600 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for IOTX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,603 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ICX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,605 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ICP/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,607 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for IOTA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,609 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for IOST/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,612 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for KAVA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,614 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for KNC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,617 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for KSM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,619 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for LRC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,730 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for MANA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,733 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for MTL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,736 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for NEO/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,739 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ONT/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,741 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for OGN/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,743 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ONE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,746 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for PEOPLE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,749 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for RLC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,752 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for RUNE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,754 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for RVN/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,756 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for RSR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,759 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ROSE/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,761 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SNX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,764 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SAND/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,805 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SUSHI/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,808 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SKL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,810 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for SXP/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,813 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for STORJ/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,817 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for TRX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,819 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for TRB/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,822 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for TLM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,824 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for THETA/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,862 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for VET/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,865 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for YFI/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,868 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ZIL/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,870 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ZEN/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,872 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ZRX/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,875 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for ZEC/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,914 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for XLM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,917 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for XTZ/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,919 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for XMR/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,922 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for QTUM/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,924 - freqtrade.data.history.datahandlers.idatahandler - WARNING - No history for 1INCH/USDT:USDT, futures, 5m found. Use `freqtrade download-data` to download the data
2025-06-02 14:57:59,927 - freqtrade.optimize.backtesting - INFO - Loading data from 2025-01-01 00:00:00 up to 2025-04-29 09:05:00 (118 days).
2025-06-02 14:57:59,934 - freqtrade.data.history.datahandlers.idatahandler - WARNING - AAVE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:59,945 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ALICE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:59,957 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ARPA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:59,968 - freqtrade.data.history.datahandlers.idatahandler - WARNING - AVAX/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:57:59,979 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ATOM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:57:59,991 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ANKR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,005 - freqtrade.data.history.datahandlers.idatahandler - WARNING - AXS/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,026 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ADA/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:00,040 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ALGO/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,056 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BAND/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,069 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BEL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,103 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BTC/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:00,119 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BAT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,134 - freqtrade.data.history.datahandlers.idatahandler - WARNING - CHR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,146 - freqtrade.data.history.datahandlers.idatahandler - WARNING - C98/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,157 - freqtrade.data.history.datahandlers.idatahandler - WARNING - COTI/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,169 - freqtrade.data.history.datahandlers.idatahandler - WARNING - CHZ/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,180 - freqtrade.data.history.datahandlers.idatahandler - WARNING - COMP/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,193 - freqtrade.data.history.datahandlers.idatahandler - WARNING - CRV/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,205 - freqtrade.data.history.datahandlers.idatahandler - WARNING - CELO/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,217 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DUSK/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,229 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DOGE/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:00,241 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DENT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,253 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DASH/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,265 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DOT/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:00,276 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DYDX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,288 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ENJ/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,300 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ETH/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:00,311 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ETC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,323 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ENS/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,338 - freqtrade.data.history.datahandlers.idatahandler - WARNING - EGLD/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,351 - freqtrade.data.history.datahandlers.idatahandler - WARNING - FIL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,363 - freqtrade.data.history.datahandlers.idatahandler - WARNING - FLM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,374 - freqtrade.data.history.datahandlers.idatahandler - WARNING - GRT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,386 - freqtrade.data.history.datahandlers.idatahandler - WARNING - GALA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,397 - freqtrade.data.history.datahandlers.idatahandler - WARNING - HBAR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,409 - freqtrade.data.history.datahandlers.idatahandler - WARNING - HOT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,420 - freqtrade.data.history.datahandlers.idatahandler - WARNING - IOTX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,431 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ICX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,443 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ICP/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,454 - freqtrade.data.history.datahandlers.idatahandler - WARNING - IOTA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,466 - freqtrade.data.history.datahandlers.idatahandler - WARNING - IOST/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,477 - freqtrade.data.history.datahandlers.idatahandler - WARNING - KAVA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,489 - freqtrade.data.history.datahandlers.idatahandler - WARNING - KNC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,500 - freqtrade.data.history.datahandlers.idatahandler - WARNING - KSM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,511 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LRC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,522 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LTC/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:00,534 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LINK/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:00,545 - freqtrade.data.history.datahandlers.idatahandler - WARNING - NEAR/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:00,557 - freqtrade.data.history.datahandlers.idatahandler - WARNING - MANA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,568 - freqtrade.data.history.datahandlers.idatahandler - WARNING - MTL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,579 - freqtrade.data.history.datahandlers.idatahandler - WARNING - NEO/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,590 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ONT/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,602 - freqtrade.data.history.datahandlers.idatahandler - WARNING - OGN/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,613 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ONE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,624 - freqtrade.data.history.datahandlers.idatahandler - WARNING - PEOPLE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,636 - freqtrade.data.history.datahandlers.idatahandler - WARNING - RLC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,648 - freqtrade.data.history.datahandlers.idatahandler - WARNING - RUNE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,659 - freqtrade.data.history.datahandlers.idatahandler - WARNING - RVN/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,671 - freqtrade.data.history.datahandlers.idatahandler - WARNING - RSR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,682 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ROSE/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,693 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SNX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,704 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SAND/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,716 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SOL/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:00,727 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SUSHI/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,739 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SKL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,751 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SXP/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,762 - freqtrade.data.history.datahandlers.idatahandler - WARNING - STORJ/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,773 - freqtrade.data.history.datahandlers.idatahandler - WARNING - TRX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,785 - freqtrade.data.history.datahandlers.idatahandler - WARNING - TRB/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,796 - freqtrade.data.history.datahandlers.idatahandler - WARNING - TLM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,809 - freqtrade.data.history.datahandlers.idatahandler - WARNING - THETA/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,821 - freqtrade.data.history.datahandlers.idatahandler - WARNING - UNI/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:00,833 - freqtrade.data.history.datahandlers.idatahandler - WARNING - VET/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,845 - freqtrade.data.history.datahandlers.idatahandler - WARNING - YFI/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,855 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ZIL/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,866 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ZEN/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,877 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ZRX/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,889 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ZEC/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,901 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XRP/USDT:USDT, funding_rate, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:00,911 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XLM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,923 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XTZ/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,935 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XMR/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,947 - freqtrade.data.history.datahandlers.idatahandler - WARNING - QTUM/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:00,958 - freqtrade.data.history.datahandlers.idatahandler - WARNING - 1INCH/USDT:USDT, funding_rate, 8h, data starts at 2025-04-25 00:00:00
2025-06-02 14:58:01,004 - freqtrade.data.history.datahandlers.idatahandler - WARNING - AVAX/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:01,049 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ADA/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:01,106 - freqtrade.data.history.datahandlers.idatahandler - WARNING - BTC/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:01,235 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DOGE/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:01,271 - freqtrade.data.history.datahandlers.idatahandler - WARNING - DOT/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:01,308 - freqtrade.data.history.datahandlers.idatahandler - WARNING - ETH/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:01,592 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LTC/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:01,607 - freqtrade.data.history.datahandlers.idatahandler - WARNING - LINK/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:01,623 - freqtrade.data.history.datahandlers.idatahandler - WARNING - NEAR/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:01,845 - freqtrade.data.history.datahandlers.idatahandler - WARNING - SOL/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:01,957 - freqtrade.data.history.datahandlers.idatahandler - WARNING - UNI/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:02,044 - freqtrade.data.history.datahandlers.idatahandler - WARNING - XRP/USDT:USDT, mark, 8h, data starts at 2025-03-28 00:00:00
2025-06-02 14:58:04,457 - freqtrade.optimize.backtesting - INFO - Dataload complete. Calculating indicators
2025-06-02 14:58:04,459 - freqtrade.optimize.backtesting - WARNING - Backtest result caching disabled due to use of open-ended timerange.
2025-06-02 14:58:04,460 - freqtrade.optimize.backtesting - INFO - Running backtesting for Strategy Pivot7Improved
2025-06-02 14:58:04,460 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-06-02 14:58:04,461 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-06-02 14:58:04,462 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
/freqtrade/user_data/strategies/pivot7_improved.py:71: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['resistance'] = dataframe.loc[dataframe['is_pivot_high'], 'high'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:72: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['support'] = dataframe.loc[dataframe['is_pivot_low'], 'low'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:71: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['resistance'] = dataframe.loc[dataframe['is_pivot_high'], 'high'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:72: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['support'] = dataframe.loc[dataframe['is_pivot_low'], 'low'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:71: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['resistance'] = dataframe.loc[dataframe['is_pivot_high'], 'high'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:72: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['support'] = dataframe.loc[dataframe['is_pivot_low'], 'low'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:71: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['resistance'] = dataframe.loc[dataframe['is_pivot_high'], 'high'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:72: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['support'] = dataframe.loc[dataframe['is_pivot_low'], 'low'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:71: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['resistance'] = dataframe.loc[dataframe['is_pivot_high'], 'high'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:72: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['support'] = dataframe.loc[dataframe['is_pivot_low'], 'low'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:71: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['resistance'] = dataframe.loc[dataframe['is_pivot_high'], 'high'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:72: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['support'] = dataframe.loc[dataframe['is_pivot_low'], 'low'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:71: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['resistance'] = dataframe.loc[dataframe['is_pivot_high'], 'high'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:72: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['support'] = dataframe.loc[dataframe['is_pivot_low'], 'low'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:71: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['resistance'] = dataframe.loc[dataframe['is_pivot_high'], 'high'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:72: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['support'] = dataframe.loc[dataframe['is_pivot_low'], 'low'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:71: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['resistance'] = dataframe.loc[dataframe['is_pivot_high'], 'high'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:72: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['support'] = dataframe.loc[dataframe['is_pivot_low'], 'low'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:71: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['resistance'] = dataframe.loc[dataframe['is_pivot_high'], 'high'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:72: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['support'] = dataframe.loc[dataframe['is_pivot_low'], 'low'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:71: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['resistance'] = dataframe.loc[dataframe['is_pivot_high'], 'high'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:72: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['support'] = dataframe.loc[dataframe['is_pivot_low'], 'low'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:71: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['resistance'] = dataframe.loc[dataframe['is_pivot_high'], 'high'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:72: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['support'] = dataframe.loc[dataframe['is_pivot_low'], 'low'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:71: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['resistance'] = dataframe.loc[dataframe['is_pivot_high'], 'high'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:72: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['support'] = dataframe.loc[dataframe['is_pivot_low'], 'low'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:71: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['resistance'] = dataframe.loc[dataframe['is_pivot_high'], 'high'].fillna(method='ffill')
/freqtrade/user_data/strategies/pivot7_improved.py:72: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.
  dataframe['support'] = dataframe.loc[dataframe['is_pivot_low'], 'low'].fillna(method='ffill')
2025-06-02 14:58:04,764 - freqtrade.optimize.backtesting - INFO - Backtesting with data from 2025-01-01 00:00:00 up to 2025-04-29 09:05:00 (118 days).
2025-06-02 14:58:07,401 - freqtrade.misc - INFO - dumping json to "/freqtrade/user_data/backtest_results/backtest-result-2025-06-02_14-58-07.meta.json"
Result for strategy Pivot7Improved
                                                BACKTESTING REPORT                                                 
┏━━━━━━━━━━━━━━━━┳━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┓
┃           Pair ┃ Trades ┃ Avg Profit % ┃ Tot Profit USDT ┃ Tot Profit % ┃ Avg Duration ┃  Win  Draw  Loss  Win% ┃
┡━━━━━━━━━━━━━━━━╇━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━┩
│  SOL/USDT:USDT │      1 │         2.37 │           0.114 │         0.11 │      0:15:00 │    1     0     0   100 │
│  XRP/USDT:USDT │      4 │         0.55 │           0.109 │         0.11 │      0:16:00 │    2     0     2  50.0 │
│ DOGE/USDT:USDT │      1 │         1.09 │           0.054 │         0.05 │      0:15:00 │    1     0     0   100 │
│  ETH/USDT:USDT │      1 │         1.18 │           0.054 │         0.05 │      0:10:00 │    1     0     0   100 │
│  UNI/USDT:USDT │      1 │         1.82 │           0.049 │         0.05 │      0:10:00 │    1     0     0   100 │
│ NEAR/USDT:USDT │      2 │         0.03 │           0.013 │         0.01 │      0:08:00 │    1     0     1  50.0 │
│ AVAX/USDT:USDT │      0 │          0.0 │           0.000 │          0.0 │         0:00 │    0     0     0     0 │
│  BTC/USDT:USDT │      0 │          0.0 │           0.000 │          0.0 │         0:00 │    0     0     0     0 │
│  DOT/USDT:USDT │      0 │          0.0 │           0.000 │          0.0 │         0:00 │    0     0     0     0 │
│ LINK/USDT:USDT │      0 │          0.0 │           0.000 │          0.0 │         0:00 │    0     0     0     0 │
│  BNB/USDT:USDT │      1 │        -0.45 │          -0.017 │        -0.02 │      0:15:00 │    0     0     1     0 │
│  LTC/USDT:USDT │      1 │         -0.5 │          -0.025 │        -0.02 │      0:25:00 │    0     0     1     0 │
│  ADA/USDT:USDT │      3 │        -0.99 │          -0.146 │        -0.15 │      0:05:00 │    0     0     3     0 │
│ ATOM/USDT:USDT │      3 │        -1.21 │          -0.182 │        -0.18 │      0:07:00 │    0     0     3     0 │
│          TOTAL │     18 │         0.07 │           0.024 │         0.02 │      0:11:00 │    7     0    11  38.9 │
└────────────────┴────────┴──────────────┴─────────────────┴──────────────┴──────────────┴────────────────────────┘
                                         LEFT OPEN TRADES REPORT                                          
┏━━━━━━━┳━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┓
┃  Pair ┃ Trades ┃ Avg Profit % ┃ Tot Profit USDT ┃ Tot Profit % ┃ Avg Duration ┃  Win  Draw  Loss  Win% ┃
┡━━━━━━━╇━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━┩
│ TOTAL │      0 │          0.0 │           0.000 │          0.0 │         0:00 │    0     0     0     0 │
└───────┴────────┴──────────────┴─────────────────┴──────────────┴──────────────┴────────────────────────┘
                                                ENTER TAG STATS                                                
┏━━━━━━━━━━━┳━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ Enter Tag ┃ Entries ┃ Avg Profit % ┃ Tot Profit USDT ┃ Tot Profit % ┃ Avg Duration ┃  Win  Draw  Loss  Win% ┃
┡━━━━━━━━━━━╇━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━┩
│     OTHER │      18 │         0.07 │           0.024 │         0.02 │      0:11:00 │    7     0    11  38.9 │
│     TOTAL │      18 │         0.07 │           0.024 │         0.02 │      0:11:00 │    7     0    11  38.9 │
└───────────┴─────────┴──────────────┴─────────────────┴──────────────┴──────────────┴────────────────────────┘
                                                  EXIT REASON STATS                                                   
┏━━━━━━━━━━━━━━━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┓
┃        Exit Reason ┃ Exits ┃ Avg Profit % ┃ Tot Profit USDT ┃ Tot Profit % ┃ Avg Duration ┃  Win  Draw  Loss  Win% ┃
┡━━━━━━━━━━━━━━━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━┩
│ trailing_stop_loss │     3 │         0.69 │           0.059 │         0.06 │      0:07:00 │    2     0     1  66.7 │
│        exit_signal │    15 │        -0.06 │          -0.034 │        -0.03 │      0:12:00 │    5     0    10  33.3 │
│              TOTAL │    18 │         0.07 │           0.024 │         0.02 │      0:11:00 │    7     0    11  38.9 │
└────────────────────┴───────┴──────────────┴─────────────────┴──────────────┴──────────────┴────────────────────────┘
                                                          MIXED TAG STATS                                                          
┏━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ Enter Tag ┃        Exit Reason ┃ Trades ┃ Avg Profit % ┃ Tot Profit USDT ┃ Tot Profit % ┃ Avg Duration ┃  Win  Draw  Loss  Win% ┃
┡━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━┩
│           │ trailing_stop_loss │      3 │         0.69 │           0.059 │         0.06 │      0:07:00 │    2     0     1  66.7 │
│           │        exit_signal │     15 │        -0.06 │          -0.034 │        -0.03 │      0:12:00 │    5     0    10  33.3 │
│     TOTAL │                    │     18 │         0.07 │           0.024 │         0.02 │      0:11:00 │    7     0    11  38.9 │
└───────────┴────────────────────┴────────┴──────────────┴─────────────────┴──────────────┴──────────────┴────────────────────────┘
                         SUMMARY METRICS                          
┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ Metric                        ┃ Value                          ┃
┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩
│ Backtesting from              │ 2025-01-01 00:00:00            │
│ Backtesting to                │ 2025-04-29 09:05:00            │
│ Trading Mode                  │ Isolated Futures               │
│ Max open trades               │ 14                             │
│                               │                                │
│ Total/Daily Avg Trades        │ 18 / 0.15                      │
│ Starting balance              │ 100 USDT                       │
│ Final balance                 │ 100.024 USDT                   │
│ Absolute profit               │ 0.024 USDT                     │
│ Total profit %                │ 0.02%                          │
│ CAGR %                        │ 0.08%                          │
│ Sortino                       │ 0.13                           │
│ Sharpe                        │ 0.06                           │
│ Calmar                        │ 1.78                           │
│ SQN                           │ 0.09                           │
│ Profit factor                 │ 1.05                           │
│ Expectancy (Ratio)            │ 0.00 (0.03)                    │
│ Avg. daily profit %           │ 0.00%                          │
│ Avg. stake amount             │ 4.697 USDT                     │
│ Total trade volume            │ 846.226 USDT                   │
│                               │                                │
│ Long / Short                  │ 10 / 8                         │
│ Total profit Long %           │ 0.17%                          │
│ Total profit Short %          │ -0.15%                         │
│ Absolute profit Long          │ 0.171 USDT                     │
│ Absolute profit Short         │ -0.147 USDT                    │
│                               │                                │
│ Best Pair                     │ SOL/USDT:USDT 0.11%            │
│ Worst Pair                    │ ATOM/USDT:USDT -0.18%          │
│ Best trade                    │ XRP/USDT:USDT 2.65%            │
│ Worst trade                   │ ATOM/USDT:USDT -2.35%          │
│ Best day                      │ 0.132 USDT                     │
│ Worst day                     │ -0.132 USDT                    │
│ Days win/draw/lose            │ 6 / 93 / 10                    │
│ Min/Max/Avg. Duration Winners │ 0d 00:10 / 0d 00:20 / 0d 00:14 │
│ Min/Max/Avg. Duration Losers  │ 0d 00:05 / 0d 00:25 / 0d 00:10 │
│ Max Consecutive Wins / Loss   │ 4 / 5                          │
│ Rejected Entry signals        │ 0                              │
│ Entry/Exit Timeouts           │ 0 / 0                          │
│                               │                                │
│ Min balance                   │ 99.868 USDT                    │
│ Max balance                   │ 100.157 USDT                   │
│ Max % of account underwater   │ 0.22%                          │
│ Absolute Drawdown (Account)   │ 0.22%                          │
│ Absolute Drawdown             │ 0.223 USDT                     │
│ Drawdown high                 │ 0.157 USDT                     │
│ Drawdown low                  │ -0.066 USDT                    │
│ Drawdown Start                │ 2025-01-26 17:50:00            │
│ Drawdown End                  │ 2025-03-14 14:10:00            │
│ Market change                 │ -26.85%                        │
└───────────────────────────────┴────────────────────────────────┘

Backtested 2025-01-01 00:00:00 -> 2025-04-29 09:05:00 | Max open trades : 14
                                                           STRATEGY SUMMARY                                                            
┏━━━━━━━━━━━━━━━━┳━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━┓
┃       Strategy ┃ Trades ┃ Avg Profit % ┃ Tot Profit USDT ┃ Tot Profit % ┃ Avg Duration ┃  Win  Draw  Loss  Win% ┃          Drawdown ┃
┡━━━━━━━━━━━━━━━━╇━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━┩
│ Pivot7Improved │     18 │         0.07 │           0.024 │         0.02 │      0:11:00 │    7     0    11  38.9 │ 0.223 USDT  0.22% │
└────────────────┴────────┴──────────────┴─────────────────┴──────────────┴──────────────┴────────────────────────┴───────────────────┘
