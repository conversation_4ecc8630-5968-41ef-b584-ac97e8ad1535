from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame
import numpy as np
from freqtrade.persistence import Trade
from datetime import datetime
import freqtrade.vendor.qtpylib.indicators as qtpylib
import talib.abstract as ta

class CryptoFuturesEdge(IStrategy):
    """
    Optimized strategy for crypto futures market, balancing risk and reward with adaptive logic.
    Combines pivot points, RSI, EMA, and volume analysis with breakout detection.
    """
    
    # Core settings
    stoploss = -0.01  # Tight 1% stoploss for capital preservation
    can_short = True
    timeframe = '15m'
    
    # Dynamic trailing stop
    trailing_stop = True
    trailing_stop_positive = 0.003  # Trail from 0.3% profit
    trailing_stop_positive_offset = 0.006  # 0.6% trail distance
    
    # Flexible ROI ladder for profit maximization
    minimal_roi = {
        "0": 0.10,    # 10% max profit
        "15": 0.06,   # 6% after 15 minutes
        "30": 0.04,   # 4% after 30 minutes
        "60": 0.02,   # 2% after 1 hour
        "120": 0.01   # 1% after 2 hours
    }
    
    startup_candle_count = 100
    
    # Market regime thresholds
    bull_threshold = 0.04
    bear_threshold = -0.02
    consolidation_threshold = 0.015

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """Core indicators for market analysis"""
        
        # RSI for momentum
        dataframe['rsi_3'] = ta.RSI(dataframe, timeperiod=3)
        dataframe['rsi_8'] = ta.RSI(dataframe, timeperiod=8)
        dataframe['rsi_14'] = ta.RSI(dataframe, timeperiod=14)
        
        # EMAs for trend
        dataframe['ema_8'] = ta.EMA(dataframe, timeperiod=8)
        dataframe['ema_21'] = ta.EMA(dataframe, timeperiod=21)
        dataframe['ema_55'] = ta.EMA(dataframe, timeperiod=55)
        
        # Pivot points for market structure
        length = 21
        dataframe['pivot_high'] = dataframe['high'].rolling(window=length, min_periods=1).max().shift(length//2)
        dataframe['pivot_high'] = dataframe['pivot_high'].where(
            dataframe['high'].shift(length//2) == dataframe['pivot_high'], np.nan
        )
        dataframe['pivot_low'] = dataframe['low'].rolling(window=length, min_periods=1).min().shift(length//2)
        dataframe['pivot_low'] = dataframe['pivot_low'].where(
            dataframe['low'].shift(length//2) == dataframe['pivot_low'], np.nan
        )
        dataframe['support'] = dataframe['pivot_low'].fillna(method='ffill')
        dataframe['resistance'] = dataframe['pivot_high'].ffill()
        
        # Volatility and volume
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)
        dataframe['volatility'] = dataframe['atr'] / dataframe['close']
        dataframe['volume_sma'] = dataframe['volume'].rolling(20).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        
        # MACD for momentum confirmation
        dataframe['macd'], dataframe['macdsignal'], _ = ta.MACD(dataframe, fastperiod=8, slowperiod=21, signalperiod=5)
        
        # Bollinger Bands for breakout detection
        dataframe['bb_upper'], dataframe['bb_middle'], dataframe['bb_lower'] = ta.BBANDS(dataframe, timeperiod=20, nbdevup=2.0, nbdevdn=2.0)
        dataframe['bb_width'] = (dataframe['bb_upper'] - dataframe['bb_lower']) / dataframe['bb_middle']
        
        # Market regime detection
        dataframe['regime_score'] = dataframe['close'].pct_change(50).rolling(10).mean()
        
        # Composite momentum score (0-100)
        momentum_signals = [
            (dataframe['rsi_8'] > 50).astype(int) * 25,
            (dataframe['macd'] > dataframe['macdsignal']).astype(int) * 25,
            (dataframe['close'] > dataframe['ema_21']).astype(int) * 25,
            (dataframe['volume_ratio'] > 1.2).astype(int) * 25
        ]
        dataframe['momentum_score'] = sum(momentum_signals)
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """Adaptive entry system with regime awareness and breakout detection"""
        
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        
        # Market regime detection
        bull_market = dataframe['regime_score'] > self.bull_threshold
        bear_market = dataframe['regime_score'] < self.bear_threshold
        consolidation = abs(dataframe['regime_score']) < self.consolidation_threshold
        
        # Core signals
        rsi_long = qtpylib.crossed_above(dataframe['rsi_3'], dataframe['rsi_8'])
        rsi_short = qtpylib.crossed_below(dataframe['rsi_3'], dataframe['rsi_8'])
        
        # Trend alignment
        trend_long = (
            (dataframe['ema_8'] > dataframe['ema_21']) &
            (dataframe['close'] > dataframe['ema_8'])
        )
        trend_short = (
            (dataframe['ema_8'] < dataframe['ema_21']) &
            (dataframe['close'] < dataframe['ema_8'])
        )
        
        # Momentum and volume
        momentum_long = (
            (dataframe['momentum_score'] >= 50) &
            (dataframe['macd'] > dataframe['macdsignal']) &
            (dataframe['rsi_14'] > 40) & (dataframe['rsi_14'] < 70)
        )
        momentum_short = (
            (dataframe['momentum_score'] <= 50) &
            (dataframe['macd'] < dataframe['macdsignal']) &
            (dataframe['rsi_14'] < 60) & (dataframe['rsi_14'] > 30)
        )
        
        # Support/resistance and volatility
        sr_long = (
            (dataframe['close'] > dataframe['support'] * 1.005) &
            (dataframe['close'] < dataframe['resistance'] * 0.98)
        )
        sr_short = (
            (dataframe['close'] < dataframe['resistance'] * 0.995) &
            (dataframe['close'] > dataframe['support'] * 1.02)
        )
        
        volatility_filter = dataframe['volatility'] > 0.005
        
        # Breakout detection
        breakout_long = (
            (dataframe['bb_width'].shift(1) < dataframe['bb_width'].rolling(20).quantile(0.3)) &
            (dataframe['close'] > dataframe['bb_upper']) &
            (dataframe['volume_ratio'] > 1.5)
        )
        breakout_short = (
            (dataframe['bb_width'].shift(1) < dataframe['bb_width'].rolling(20).quantile(0.3)) &
            (dataframe['close'] < dataframe['bb_lower']) &
            (dataframe['volume_ratio'] > 1.5)
        )
        
        # Regime-specific conditions
        regime_long = (
            (bull_market & (dataframe['rsi_14'] > 35)) |
            (consolidation & (dataframe['rsi_14'] > 40) & (dataframe['momentum_score'] >= 50)) |
            (bear_market & (dataframe['rsi_14'] < 35) & (dataframe['momentum_score'] >= 75))
        )
        regime_short = (
            (bear_market & (dataframe['rsi_14'] < 65)) |
            (consolidation & (dataframe['rsi_14'] < 60) & (dataframe['momentum_score'] <= 50)) |
            (bull_market & (dataframe['rsi_14'] > 65) & (dataframe['momentum_score'] <= 25))
        )
        
        # Final entry logic
        long_entry = (
            (rsi_long & trend_long & momentum_long & sr_long & volatility_filter & regime_long) |
            breakout_long
        )
        short_entry = (
            (rsi_short & trend_short & momentum_short & sr_short & volatility_filter & regime_short) |
            breakout_short
        )
        
        dataframe.loc[long_entry, 'enter_long'] = 1
        dataframe.loc[short_entry, 'enter_short'] = 1
        
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """Exit system to lock in profits and limit losses"""
        
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        
        # Long exits
        long_exit = (
            (qtpylib.crossed_below(dataframe['rsi_3'], dataframe['rsi_8'])) |
            (dataframe['rsi_14'] > 75) |
            (dataframe['close'] < dataframe['ema_8']) |
            (dataframe['macd'] < dataframe['macdsignal'])
        )
        
        # Short exits
        short_exit = (
            (qtpylib.crossed_above(dataframe['rsi_3'], dataframe['rsi_8'])) |
            (dataframe['rsi_14'] < 25) |
            (dataframe['close'] > dataframe['ema_8']) |
            (dataframe['macd'] > dataframe['macdsignal'])
        )
        
        dataframe.loc[long_exit, 'exit_long'] = 1
        dataframe.loc[short_exit, 'exit_short'] = 1
        
        return dataframe

    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """Adaptive stoploss based on ATR and trade duration"""
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if len(dataframe) == 0:
            return self.stoploss
            
        current_atr = dataframe['atr'].iloc[-1]
        current_close = dataframe['close'].iloc[-1]
        minutes_in_trade = (current_time - trade.open_date_utc).total_seconds() / 60
        
        atr_stop = -(current_atr * 1.5) / current_close
        
        if current_profit > 0.03:  # 3%+ profit
            return max(-0.005, atr_stop * 0.5)  # Tight trailing
        elif current_profit > 0.015:  # 1.5%+ profit
            return max(-0.008, atr_stop * 0.75)
        elif minutes_in_trade > 60:  # 1 hour+ in trade
            return max(-0.01, atr_stop * 0.9)
        else:
            return max(-0.015, atr_stop)

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:
        """Dynamic leverage based on momentum and volatility"""
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if len(dataframe) == 0:
            return 5.0
            
        momentum_score = dataframe['momentum_score'].iloc[-1]
        volatility = dataframe['volatility'].iloc[-1]
        
        base_leverage = 5.0 if momentum_score >= 50 else 3.0
        vol_multiplier = 0.5 if volatility > 0.02 else (0.75 if volatility > 0.01 else 1.0)
        
        final_leverage = base_leverage * vol_multiplier
        return min(final_leverage, max_leverage, 8.0)  # Cap at 8x

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float,
                           rate: float, time_in_force: str, current_time: datetime,
                           entry_tag: str, side: str, **kwargs) -> bool:
        """Final validation for trade entry"""
        
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if len(dataframe) == 0:
            return False
            
        volatility = dataframe['volatility'].iloc[-1]
        volume_ratio = dataframe['volume_ratio'].iloc[-1]
        
        if volatility < 0.003 or volume_ratio < 0.6:
            return False
            
        return True