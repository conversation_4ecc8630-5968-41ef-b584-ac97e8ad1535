from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame
import numpy as np
from freqtrade.persistence import Trade
from datetime import datetime, timedelta
import freqtrade.vendor.qtpylib.indicators as qtpylib
import talib.abstract as ta
from typing import Optional
import logging

class UltimateAdaptiveStrategy(IStrategy):
    """
    The strategy I'd actually use with my own money.
    Combines multiple edge cases, regime detection, and adaptive risk management.
    """
    
    # Core settings - Conservative but aggressive when conditions are right
    stoploss = -0.008  # Very tight 0.8% - capital preservation is key
    can_short = True
    timeframe = '15m'
    
    # Dynamic trailing - starts immediately
    trailing_stop = True
    trailing_stop_positive = 0.002  # Trail from 0.2% profit
    trailing_stop_positive_offset = 0.004  # 0.4% trail distance
    
    # Aggressive ROI ladder - take profits fast
    minimal_roi = {
        "0": 0.025,   # 2.5% max
        "5": 0.015,   # 1.5% after 5min  
        "10": 0.01,   # 1% after 10min
        "20": 0.006,  # 0.6% after 20min
        "45": 0.003,  # 0.3% after 45min
        "90": 0.001   # 0.1% after 1.5h
    }
    
    startup_candle_count = 200
    
    # Market regime parameters
    bull_market_threshold = 0.05
    bear_market_threshold = -0.03
    consolidation_threshold = 0.02
    
    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """Build a comprehensive market analysis framework"""
        
        # ========== CORE INDICATORS ==========
        # Multi-timeframe RSI
        dataframe['rsi_2'] = ta.RSI(dataframe, timeperiod=2)
        dataframe['rsi_4'] = ta.RSI(dataframe, timeperiod=4)
        dataframe['rsi_8'] = ta.RSI(dataframe, timeperiod=8)
        dataframe['rsi_14'] = ta.RSI(dataframe, timeperiod=14)
        dataframe['rsi_21'] = ta.RSI(dataframe, timeperiod=21)
        
        # Adaptive RSI - changes based on volatility
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)
        dataframe['volatility'] = dataframe['atr'] / dataframe['close']
        
        # Dynamic RSI period based on volatility
        high_vol_periods = np.where(dataframe['volatility'] > dataframe['volatility'].rolling(50).quantile(0.7), 8, 14)
        low_vol_periods = np.where(dataframe['volatility'] < dataframe['volatility'].rolling(50).quantile(0.3), 21, 14)
        
        # ========== TREND ANALYSIS ==========
        # Multiple EMA system
        for period in [8, 13, 21, 34, 55, 89]:
            dataframe[f'ema_{period}'] = ta.EMA(dataframe, timeperiod=period)
        
        # Trend strength and direction
        dataframe['trend_strength'] = (dataframe['ema_8'] - dataframe['ema_34']) / dataframe['close']
        dataframe['trend_direction'] = np.where(dataframe['ema_8'] > dataframe['ema_21'], 1, -1)
        
        # Market regime detection
        dataframe['regime_score'] = (
            dataframe['close'].pct_change(50).rolling(10).mean()
        )
        
        # ========== PIVOT SYSTEM (ENHANCED) ==========
        # Multiple pivot lengths for different timeframes
        for length in [13, 21, 34]:
            # Bias-free pivot calculation
            dataframe[f'pivot_high_{length}'] = dataframe['high'].rolling(
                window=length, min_periods=1
            ).max().shift(length//2)
            
            dataframe[f'pivot_low_{length}'] = dataframe['low'].rolling(
                window=length, min_periods=1
            ).min().shift(length//2)
            
            # Only mark true pivots
            dataframe[f'pivot_high_{length}'] = dataframe[f'pivot_high_{length}'].where(
                dataframe['high'].shift(length//2) == dataframe[f'pivot_high_{length}'], np.nan
            )
            dataframe[f'pivot_low_{length}'] = dataframe[f'pivot_low_{length}'].where(
                dataframe['low'].shift(length//2) == dataframe[f'pivot_low_{length}'], np.nan
            )
        
        # Dynamic support/resistance
        dataframe['support'] = np.minimum.reduce([
        dataframe['pivot_low_13'].ffill(),
        dataframe['pivot_low_21'].ffill(),
        dataframe['pivot_low_34'].ffill()
         ])

        dataframe['resistance'] = np.maximum.reduce([
         dataframe['pivot_high_13'].ffill(),
         dataframe['pivot_high_21'].ffill(),
         dataframe['pivot_high_34'].ffill()
    ])
        
        # ========== MOMENTUM & OSCILLATORS ==========
        dataframe['macd'], dataframe['macdsignal'], dataframe['macdhist'] = ta.MACD(dataframe['close'])
        dataframe['stoch_k'], dataframe['stoch_d'] = ta.STOCH(dataframe['high'], dataframe['low'], dataframe['close'])
        dataframe['cci'] = ta.CCI(dataframe, timeperiod=20)
        dataframe['williams_r'] = ta.WILLR(dataframe, timeperiod=14)
        
        # ========== VOLUME ANALYSIS ==========
        dataframe['volume_sma'] = dataframe['volume'].rolling(20).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        dataframe['vwap'] = qtpylib.rolling_vwap(dataframe, window=20)  # Volume weighted average price
        
        # On Balance Volume
        dataframe['obv'] = ta.OBV(dataframe)
        dataframe['obv_sma'] = dataframe['obv'].rolling(20).mean()
        
        # ========== VOLATILITY MEASURES ==========
        dataframe['bb_upper'], dataframe['bb_middle'], dataframe['bb_lower'] = ta.BBANDS(
         dataframe['close'], timeperiod=20, nbdevup=2.0, nbdevdn=2.0
        )
        dataframe['bb_width'] = (dataframe['bb_upper'] - dataframe['bb_lower']) / dataframe['bb_middle']
        dataframe['bb_position'] = (dataframe['close'] - dataframe['bb_lower']) / (dataframe['bb_upper'] - dataframe['bb_lower'])
        
        # ========== MARKET MICROSTRUCTURE ==========
        # Price action patterns
        dataframe['doji'] = abs(dataframe['open'] - dataframe['close']) <= (dataframe['high'] - dataframe['low']) * 0.1
        dataframe['hammer'] = (
            (dataframe['close'] > dataframe['open']) &  # Green candle
            ((dataframe['open'] - dataframe['low']) > 2 * (dataframe['close'] - dataframe['open'])) &  # Long lower shadow
            ((dataframe['high'] - dataframe['close']) < (dataframe['close'] - dataframe['open']))  # Short upper shadow
        )
        dataframe['shooting_star'] = (
            (dataframe['open'] > dataframe['close']) &  # Red candle
            ((dataframe['high'] - dataframe['open']) > 2 * (dataframe['open'] - dataframe['close'])) &  # Long upper shadow
            ((dataframe['close'] - dataframe['low']) < (dataframe['open'] - dataframe['close']))  # Short lower shadow
        )
        
        # ========== MARKET SENTIMENT PROXIES ==========
        # Fear/Greed based on RSI extremes
        dataframe['fear_greed'] = np.where(
            dataframe['rsi_14'] < 30, -1,  # Fear
            np.where(dataframe['rsi_14'] > 70, 1, 0)  # Greed
        )
        
        # Market efficiency measure
        dataframe['efficiency'] = abs(dataframe['close'] - dataframe['close'].shift(10)) / (
            dataframe['high'].rolling(10).sum() - dataframe['low'].rolling(10).sum()
        )
        
        # ========== ADAPTIVE PARAMETERS ==========
        # Dynamic lookback periods based on volatility
        dataframe['adaptive_length'] = np.where(
            dataframe['volatility'] > dataframe['volatility'].rolling(50).median() * 1.5,
            15,  # Shorter period in high volatility
            25   # Longer period in normal/low volatility
        )
        
        # ========== COMPOSITE SIGNALS ==========
        # Bull/Bear power
        dataframe['bull_power'] = dataframe['high'] - dataframe['ema_13']
        dataframe['bear_power'] = dataframe['low'] - dataframe['ema_13']
        
        # Momentum score (0-100)
        momentum_components = [
            (dataframe['rsi_14'] > 50).astype(int) * 20,
            (dataframe['macd'] > dataframe['macdsignal']).astype(int) * 20,
            (dataframe['close'] > dataframe['ema_21']).astype(int) * 20,
            (dataframe['volume_ratio'] > 1.2).astype(int) * 20,
            (dataframe['obv'] > dataframe['obv_sma']).astype(int) * 20
        ]
        dataframe['momentum_score'] = sum(momentum_components)
        
        return dataframe
    
    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """Multi-layer entry system with regime awareness"""
        
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        
        # ========== MARKET REGIME DETECTION ==========
        bull_market = dataframe['regime_score'] > self.bull_market_threshold
        bear_market = dataframe['regime_score'] < self.bear_market_threshold
        consolidation = abs(dataframe['regime_score']) < self.consolidation_threshold
        
        # ========== LONG ENTRY CONDITIONS ==========
        # Core signal: RSI cross with multiple confirmations
        rsi_long_signal = qtpylib.crossed_above(dataframe['rsi_2'], dataframe['rsi_4'])
        
        # Trend alignment (multiple layers)
        trend_alignment_long = (
            (dataframe['ema_8'] > dataframe['ema_13']) &
            (dataframe['ema_13'] > dataframe['ema_21']) &
            (dataframe['close'] > dataframe['ema_8']) &
            (dataframe['trend_direction'] == 1)
        )
        
        # Momentum confirmation
        momentum_long = (
            (dataframe['momentum_score'] >= 60) &
            (dataframe['macd'] > dataframe['macdsignal']) &
            (dataframe['macdhist'] > dataframe['macdhist'].shift(1)) &  # Increasing histogram
            (dataframe['rsi_14'] > 45) & (dataframe['rsi_14'] < 75)  # Not oversold/overbought
        )
        
        # Volume and market structure
        volume_structure_long = (
            (dataframe['volume_ratio'] > 1.1) &
            (dataframe['close'] > dataframe['vwap']) &
            (dataframe['obv'] > dataframe['obv_sma']) &
            (~dataframe['shooting_star'])  # Avoid shooting star patterns
        )
        
        # Support/resistance awareness
        sr_long = (
            (dataframe['close'] > dataframe['support'] * 1.005) &  # Above support
            (dataframe['close'] < dataframe['resistance'] * 0.98) &  # Not at resistance
            (dataframe['bb_position'] > 0.3) & (dataframe['bb_position'] < 0.8)  # Not at BB extremes
        )
        
        # Volatility filter
        volatility_filter = (
            (dataframe['bb_width'] > dataframe['bb_width'].rolling(20).quantile(0.3)) &  # Sufficient volatility
            (dataframe['atr'] > dataframe['atr'].rolling(50).quantile(0.2))  # Above minimum ATR
        )
        
        # Regime-specific conditions
        regime_long = (
            (bull_market & (dataframe['rsi_14'] > 40)) |  # Less restrictive in bull market
            (consolidation & (dataframe['rsi_14'] > 35) & (dataframe['rsi_14'] < 65)) |  # Range trading
            (bear_market & (dataframe['rsi_14'] < 35) & (dataframe['momentum_score'] >= 80))  # Only strongest signals in bear market
        )
        
        # ========== SHORT ENTRY CONDITIONS ==========
        rsi_short_signal = qtpylib.crossed_below(dataframe['rsi_2'], dataframe['rsi_4'])
        
        trend_alignment_short = (
            (dataframe['ema_8'] < dataframe['ema_13']) &
            (dataframe['ema_13'] < dataframe['ema_21']) &
            (dataframe['close'] < dataframe['ema_8']) &
            (dataframe['trend_direction'] == -1)
        )
        
        momentum_short = (
            (dataframe['momentum_score'] <= 40) &
            (dataframe['macd'] < dataframe['macdsignal']) &
            (dataframe['macdhist'] < dataframe['macdhist'].shift(1)) &
            (dataframe['rsi_14'] < 55) & (dataframe['rsi_14'] > 25)
        )
        
        volume_structure_short = (
            (dataframe['volume_ratio'] > 1.1) &
            (dataframe['close'] < dataframe['vwap']) &
            (dataframe['obv'] < dataframe['obv_sma']) &
            (~dataframe['hammer'])
        )
        
        sr_short = (
            (dataframe['close'] < dataframe['resistance'] * 0.995) &
            (dataframe['close'] > dataframe['support'] * 1.02) &
            (dataframe['bb_position'] > 0.2) & (dataframe['bb_position'] < 0.7)
        )
        
        regime_short = (
            (bear_market & (dataframe['rsi_14'] < 60)) |
            (consolidation & (dataframe['rsi_14'] > 35) & (dataframe['rsi_14'] < 65)) |
            (bull_market & (dataframe['rsi_14'] > 65) & (dataframe['momentum_score'] <= 20))
        )
        
        # ========== FINAL ENTRY LOGIC ==========
        long_entry = (
            rsi_long_signal &
            trend_alignment_long &
            momentum_long &
            volume_structure_long &
            sr_long &
            volatility_filter &
            regime_long
        )
        
        short_entry = (
            rsi_short_signal &
            trend_alignment_short &
            momentum_short &
            volume_structure_short &
            sr_short &
            volatility_filter &
            regime_short
        )
        
        dataframe.loc[long_entry, 'enter_long'] = 1
        dataframe.loc[short_entry, 'enter_short'] = 1
        
        return dataframe
    
    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """Smart exit system with multiple triggers"""
        
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        
        # Long exits
        long_exit_conditions = (
            # RSI reversal
            (qtpylib.crossed_below(dataframe['rsi_2'], dataframe['rsi_4'])) |
            # Momentum divergence
            ((dataframe['momentum_score'] < 40) & (dataframe['rsi_14'] > 60)) |
            # Trend break
            (dataframe['close'] < dataframe['ema_8']) |
            # Volume exhaustion
            ((dataframe['volume_ratio'] < 0.6) & (dataframe['rsi_14'] > 65)) |
            # Overbought with weakness
            ((dataframe['rsi_14'] > 75) & (dataframe['macdhist'] < 0))
        )
        
        # Short exits  
        short_exit_conditions = (
            (qtpylib.crossed_above(dataframe['rsi_2'], dataframe['rsi_4'])) |
            ((dataframe['momentum_score'] > 60) & (dataframe['rsi_14'] < 40)) |
            (dataframe['close'] > dataframe['ema_8']) |
            ((dataframe['volume_ratio'] < 0.6) & (dataframe['rsi_14'] < 35)) |
            ((dataframe['rsi_14'] < 25) & (dataframe['macdhist'] > 0))
        )
        
        dataframe.loc[long_exit_conditions, 'exit_long'] = 1
        dataframe.loc[short_exit_conditions, 'exit_short'] = 1
        
        return dataframe
    
    def custom_stoploss(self, pair: str, trade: Trade, current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """Advanced adaptive stop loss system"""
        
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if len(dataframe) == 0:
                return self.stoploss
            
            # Get current market conditions
            current_atr = dataframe['atr'].iloc[-1]
            current_volatility = dataframe['volatility'].iloc[-1]
            current_bb_width = dataframe['bb_width'].iloc[-1]
            
            # Base stop using ATR
            atr_multiplier = 1.5 if current_volatility > 0.02 else 2.0
            atr_stop = -(current_atr * atr_multiplier) / current_rate
            
            # Time-based adjustments
            minutes_in_trade = (current_time - trade.open_date_utc).total_seconds() / 60
            
            if current_profit > 0.015:  # 1.5%+ profit
                # Very tight trailing
                return max(-0.003, atr_stop * 0.3)
            elif current_profit > 0.008:  # 0.8%+ profit
                # Moderate trailing
                return max(-0.005, atr_stop * 0.5)
            elif current_profit > 0.003:  # 0.3%+ profit
                # Break-even stop
                return max(-0.001, atr_stop * 0.7)
            elif minutes_in_trade > 60:  # 1 hour+ in trade
                # Time-based tightening
                return max(-0.006, atr_stop * 0.8)
            else:
                # Initial stop
                return max(-0.008, atr_stop)
                
        except Exception as e:
            return self.stoploss
    
    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, side: str,
                 **kwargs) -> float:
        """Dynamic leverage based on market conditions and confidence"""
        
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if len(dataframe) == 0:
                return 3.0
            
            # Get market conditions
            momentum_score = dataframe['momentum_score'].iloc[-1]
            volatility = dataframe['volatility'].iloc[-1]
            trend_strength = abs(dataframe['trend_strength'].iloc[-1])
            
            # Base leverage on signal strength
            if momentum_score >= 80:  # Very strong signal
                base_leverage = 8.0
            elif momentum_score >= 60:  # Strong signal
                base_leverage = 6.0
            elif momentum_score >= 40:  # Moderate signal
                base_leverage = 4.0
            else:  # Weak signal
                base_leverage = 2.0
            
            # Adjust for volatility (higher vol = lower leverage)
            if volatility > 0.025:  # High volatility
                vol_multiplier = 0.5
            elif volatility > 0.015:  # Medium volatility
                vol_multiplier = 0.7
            else:  # Low volatility
                vol_multiplier = 1.0
            
            # Adjust for trend strength (stronger trend = higher leverage)
            if trend_strength > 0.03:  # Strong trend
                trend_multiplier = 1.2
            elif trend_strength > 0.015:  # Medium trend
                trend_multiplier = 1.0
            else:  # Weak trend
                trend_multiplier = 0.8
            
            final_leverage = base_leverage * vol_multiplier * trend_multiplier
            return min(final_leverage, max_leverage, 10.0)  # Cap at 10x
            
        except Exception as e:
            return 3.0  # Safe default
    
    def confirm_trade_entry(self, pair: str, order_type: str, amount: float,
                           rate: float, time_in_force: str, current_time: datetime,
                           entry_tag: str, side: str, **kwargs) -> bool:
        """Final validation before trade execution"""
        
        try:
            dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
            if len(dataframe) == 0:
                return False
            
            # Last-minute checks
            current_volatility = dataframe['volatility'].iloc[-1]
            current_volume_ratio = dataframe['volume_ratio'].iloc[-1]
            current_bb_width = dataframe['bb_width'].iloc[-1]
            
            # Don't trade in dead markets
            if (current_volatility < 0.003 or 
                current_volume_ratio < 0.5 or 
                current_bb_width < dataframe['bb_width'].rolling(50).quantile(0.1)):
                return False
            
            # Don't trade during major gaps
            gap_size = abs(dataframe['open'].iloc[-1] - dataframe['close'].iloc[-2]) / dataframe['close'].iloc[-2]
            if gap_size > 0.02:  # 2%+ gap
                return False
            
            return True
            
        except Exception as e:
            return False